/* components/theme-selector/theme-selector.wxss */
.theme-selector {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.theme-selector.show {
  opacity: 1;
  visibility: visible;
  animation: fadeIn 0.4s ease-out;
}

.theme-selector.hide {
  opacity: 0;
  visibility: hidden;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10rpx);
}

.theme-panel {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 240, 0.95) 100%);
  backdrop-filter: blur(25rpx);
  border-radius: 48rpx 48rpx 0 0;
  padding: 50rpx 30rpx 80rpx;
  box-shadow: 0 -16rpx 48rpx rgba(0, 0, 0, 0.25);
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  transform: translateY(100%);
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
}

.theme-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 6rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 48rpx 48rpx 0 0;
}

.theme-selector.show .theme-panel {
  transform: translateY(0);
  animation: slideUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.1);
}

.panel-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2f3542;
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.close-icon {
  font-size: 28rpx;
  color: #666;
}

.themes-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx;
  margin-bottom: 40rpx;
}

.theme-item {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20rpx;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
}

.theme-item.active {
  border-color: #ff6b35;
  background: rgba(255, 107, 53, 0.1);
  transform: scale(1.05);
}

.theme-item:active {
  transform: scale(0.95);
}

.theme-preview {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  position: relative;
  margin-bottom: 20rpx;
  overflow: hidden;
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.2);
}

.preview-circle {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 30rpx;
  height: 30rpx;
  border-radius: 50%;
  opacity: 0.8;
}

.preview-bar {
  position: absolute;
  bottom: 20rpx;
  left: 20rpx;
  width: 60rpx;
  height: 8rpx;
  border-radius: 4rpx;
  opacity: 0.9;
}

.preview-dot {
  position: absolute;
  top: 50%;
  left: 20rpx;
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  transform: translateY(-50%);
  opacity: 0.8;
}

.theme-name {
  font-size: 24rpx;
  color: #2f3542;
  font-weight: 500;
  text-align: center;
}

.selected-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.4);
}

.selected-icon {
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.panel-footer {
  text-align: center;
  padding-top: 20rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.1);
}

.footer-text {
  font-size: 24rpx;
  color: #999;
}
