/* pages/order-detail/order-detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 订单状态 */
.status-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 40rpx;
  display: flex;
  align-items: center;
}

.status-icon {
  font-size: 80rpx;
  margin-right: 30rpx;
}

.status-info {
  flex: 1;
}

.status-text {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.status-desc {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

/* 通用区块样式 */
.order-info-section,
.order-items-section,
.cost-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.copy-btn {
  font-size: 24rpx;
  color: #ff6b35;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b35;
  border-radius: 20rpx;
}

/* 订单信息 */
.info-row {
  display: flex;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #666;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.info-value.address {
  line-height: 1.4;
}

/* 订单商品 */
.order-items {
  display: flex;
  flex-direction: column;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-price {
  font-size: 24rpx;
  color: #ff6b35;
}

.item-quantity {
  font-size: 24rpx;
  color: #666;
}

.item-subtotal {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 费用明细 */
.cost-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.cost-row:last-child {
  margin-bottom: 0;
}

.cost-row.total {
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  margin-top: 10rpx;
}

.cost-label {
  font-size: 28rpx;
  color: #666;
}

.cost-row.total .cost-label {
  font-weight: bold;
  color: #333;
}

.cost-value {
  font-size: 28rpx;
  color: #333;
}

.cost-value.free {
  color: #2ed573;
}

.total-amount {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 操作按钮 */
.action-section {
  margin: 20rpx;
  display: flex;
  gap: 20rpx;
}

.action-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.cancel-btn {
  background-color: #f5f5f5;
  color: #666;
}

.reorder-btn {
  background-color: #ff6b35;
  color: white;
}

.service-btn {
  background-color: #007aff;
  color: white;
}

/* 状态页面 */
.loading-container,
.error-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading,
.error {
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
