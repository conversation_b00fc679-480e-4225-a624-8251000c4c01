/* pages/category/category.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 搜索栏 */
.search-bar {
  padding: 20rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.search-input {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
}

.search-icon {
  margin-right: 20rpx;
  color: #999;
  font-size: 28rpx;
}

.search-input input {
  flex: 1;
  font-size: 28rpx;
}

/* 分类导航 */
.category-nav {
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.category-scroll {
  white-space: nowrap;
}

.category-list {
  display: flex;
  padding: 0 20rpx;
}

.category-item {
  display: inline-block;
  padding: 30rpx 40rpx;
  margin-right: 20rpx;
  border-radius: 50rpx;
  white-space: nowrap;
}

.category-item.active {
  background-color: #ff6b35;
}

.category-text {
  font-size: 28rpx;
  color: #666;
}

.category-item.active .category-text {
  color: white;
  font-weight: bold;
}

/* 菜品列表 */
.dish-list {
  padding: 20rpx;
}

.dish-item {
  display: flex;
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.dish-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.dish-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.dish-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10rpx;
}

.dish-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.dish-tags {
  display: flex;
  gap: 10rpx;
}

.tag {
  padding: 4rpx 12rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  color: white;
  font-weight: bold;
}

.tag.hot {
  background-color: #ff4757;
}

.tag.new {
  background-color: #2ed573;
}

.dish-desc {
  font-size: 24rpx;
  color: #666;
  line-height: 1.4;
  margin-bottom: 10rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.dish-stats {
  margin-bottom: 15rpx;
}

.sales-count {
  font-size: 22rpx;
  color: #999;
}

.dish-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.dish-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 状态提示 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

.load-more {
  text-align: center;
  padding: 40rpx;
  color: #999;
  font-size: 24rpx;
}

.no-more {
  text-align: center;
  padding: 40rpx;
  color: #ccc;
  font-size: 24rpx;
}
