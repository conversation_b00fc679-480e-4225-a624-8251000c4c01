#!/bin/bash

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}🍽️  美食小程序后端启动脚本${NC}"
echo -e "${BLUE}========================================${NC}"

echo ""
echo -e "${YELLOW}📋 检查环境...${NC}"

# 检查Java环境
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}')
    echo -e "${GREEN}✅ Java环境检查通过: $JAVA_VERSION${NC}"
else
    echo -e "${RED}❌ Java环境未安装或配置错误${NC}"
    echo -e "${RED}请安装Java 8或更高版本${NC}"
    exit 1
fi

# 检查Maven环境
if command -v mvn &> /dev/null; then
    MVN_VERSION=$(mvn -version 2>&1 | head -n 1 | awk '{print $3}')
    echo -e "${GREEN}✅ Maven环境检查通过: $MVN_VERSION${NC}"
else
    echo -e "${RED}❌ Maven环境未安装或配置错误${NC}"
    echo -e "${RED}请安装Maven并配置环境变量${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}📦 安装依赖...${NC}"
mvn clean install -DskipTests

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 依赖安装失败${NC}"
    exit 1
fi
echo -e "${GREEN}✅ 依赖安装完成${NC}"

echo ""
echo -e "${YELLOW}📁 创建上传目录...${NC}"
mkdir -p uploads/{dishes,banners,categories,avatars,temp}
echo -e "${GREEN}✅ 上传目录创建完成${NC}"

echo ""
echo -e "${YELLOW}🗄️  数据库初始化提示：${NC}"
echo -e "${YELLOW}请确保MySQL服务已启动${NC}"
echo -e "${YELLOW}请执行 src/main/resources/sql/init.sql 初始化数据库${NC}"
echo ""

echo -e "${YELLOW}🚀 启动应用...${NC}"
echo -e "${BLUE}应用将在 http://localhost:8080 启动${NC}"
echo -e "${BLUE}API文档: http://localhost:8080/api/doc.html${NC}"
echo -e "${BLUE}图片访问: http://localhost:8080/api/images/${NC}"
echo ""

# 启动应用
mvn spring-boot:run
