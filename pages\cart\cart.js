// pages/cart/cart.js
const { formatPrice, showConfirm } = require('../../utils/util')

Page({
  data: {
    cartItems: [],
    totalPrice: 0,
    totalCount: 0,
    selectedAll: true,
    isEmpty: true
  },

  onLoad() {
    this.loadCartData()
  },

  onShow() {
    this.loadCartData()
  },

  // 加载购物车数据
  loadCartData() {
    const app = getApp()
    const cartData = app.getCartData()
    
    const cartItems = cartData.cart.map(item => ({
      ...item,
      selected: true
    }))
    
    this.setData({
      cartItems,
      isEmpty: cartItems.length === 0
    })
    
    this.calculateTotal()
  },

  // 计算总价
  calculateTotal() {
    const { cartItems } = this.data
    let totalPrice = 0
    let totalCount = 0
    let selectedCount = 0
    
    cartItems.forEach(item => {
      totalCount += item.quantity
      if (item.selected) {
        totalPrice += item.price * item.quantity
        selectedCount += item.quantity
      }
    })
    
    this.setData({
      totalPrice: formatPrice(totalPrice),
      totalCount,
      selectedAll: selectedCount === totalCount && totalCount > 0
    })
  },

  // 选择/取消选择商品
  onSelectItem(e) {
    const { index } = e.currentTarget.dataset
    const { cartItems } = this.data
    
    cartItems[index].selected = !cartItems[index].selected
    
    this.setData({
      cartItems
    })
    
    this.calculateTotal()
  },

  // 全选/取消全选
  onSelectAll() {
    const { cartItems, selectedAll } = this.data
    const newSelectedAll = !selectedAll
    
    cartItems.forEach(item => {
      item.selected = newSelectedAll
    })
    
    this.setData({
      cartItems,
      selectedAll: newSelectedAll
    })
    
    this.calculateTotal()
  },

  // 增加数量
  onIncreaseQuantity(e) {
    const { index } = e.currentTarget.dataset
    const { cartItems } = this.data
    const app = getApp()
    
    cartItems[index].quantity += 1
    app.updateCartQuantity(cartItems[index].id, cartItems[index].quantity)
    
    this.setData({
      cartItems
    })
    
    this.calculateTotal()
  },

  // 减少数量
  onDecreaseQuantity(e) {
    const { index } = e.currentTarget.dataset
    const { cartItems } = this.data
    const app = getApp()
    
    if (cartItems[index].quantity > 1) {
      cartItems[index].quantity -= 1
      app.updateCartQuantity(cartItems[index].id, cartItems[index].quantity)
      
      this.setData({
        cartItems
      })
      
      this.calculateTotal()
    }
  },

  // 删除商品
  async onDeleteItem(e) {
    const { index } = e.currentTarget.dataset
    const { cartItems } = this.data
    const app = getApp()
    
    try {
      await showConfirm('确定要删除这个商品吗？')
      
      const itemId = cartItems[index].id
      app.removeFromCart(itemId)
      
      cartItems.splice(index, 1)
      
      this.setData({
        cartItems,
        isEmpty: cartItems.length === 0
      })
      
      this.calculateTotal()
      
      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })
    } catch (error) {
      // 用户取消删除
    }
  },

  // 清空购物车
  async onClearCart() {
    try {
      await showConfirm('确定要清空购物车吗？')
      
      const app = getApp()
      app.clearCart()
      
      this.setData({
        cartItems: [],
        totalPrice: 0,
        totalCount: 0,
        isEmpty: true
      })
      
      wx.showToast({
        title: '购物车已清空',
        icon: 'success'
      })
    } catch (error) {
      // 用户取消清空
    }
  },

  // 去结算
  onCheckout() {
    const { cartItems, totalPrice } = this.data
    const selectedItems = cartItems.filter(item => item.selected)
    
    if (selectedItems.length === 0) {
      wx.showToast({
        title: '请选择要结算的商品',
        icon: 'none'
      })
      return
    }
    
    // 跳转到订单确认页面
    wx.navigateTo({
      url: `/pages/order/order?from=cart&totalPrice=${totalPrice}`
    })
  },

  // 继续购物
  onContinueShopping() {
    wx.switchTab({
      url: '/pages/index/index'
    })
  },

  // 获取图片URL
  getImageUrl(path) {
    const { getImageUrl } = require('../../utils/util')
    return getImageUrl(path)
  }
})
