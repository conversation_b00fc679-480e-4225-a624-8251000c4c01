-- H2数据库初始化脚本

-- 菜品分类表
CREATE TABLE IF NOT EXISTS category (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(50) NOT NULL,
  icon VARCHAR(255),
  color VARCHAR(20),
  sort INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted TINYINT DEFAULT 0
);

-- 菜品表
CREATE TABLE IF NOT EXISTS dish (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  name VARCHAR(100) NOT NULL,
  description TEXT,
  price DECIMAL(10,2) NOT NULL,
  original_price DECIMAL(10,2),
  image VARCHAR(255),
  category_id BIGINT NOT NULL,
  tags VARCHAR(500),
  rating DECIMAL(3,2) DEFAULT 0.00,
  sales INT DEFAULT 0,
  is_hot BOOLEAN DEFAULT FALSE,
  is_new BOOLEAN DEFAULT FALSE,
  is_recommend BOOLEAN DEFAULT FALSE,
  discount DECIMAL(3,2) DEFAULT 1.00,
  stock INT DEFAULT 999,
  status TINYINT DEFAULT 1,
  sort INT DEFAULT 0,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted TINYINT DEFAULT 0
);

-- 菜品图片表
CREATE TABLE IF NOT EXISTS dish_image (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  dish_id BIGINT NOT NULL,
  image_url VARCHAR(255) NOT NULL,
  image_type TINYINT DEFAULT 1,
  sort INT DEFAULT 0,
  description VARCHAR(255),
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted TINYINT DEFAULT 0
);

-- 轮播图表
CREATE TABLE IF NOT EXISTS banner (
  id BIGINT AUTO_INCREMENT PRIMARY KEY,
  title VARCHAR(100) NOT NULL,
  subtitle VARCHAR(200),
  image VARCHAR(255) NOT NULL,
  link VARCHAR(255),
  type VARCHAR(20) DEFAULT 'category',
  sort INT DEFAULT 0,
  status TINYINT DEFAULT 1,
  create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  deleted TINYINT DEFAULT 0
);

-- 插入分类数据
INSERT INTO category (name, icon, color, sort) VALUES
('热门推荐', '/images/categories/hot.png', '#ff6b35', 1),
('肉类', '/images/categories/meat.png', '#e74c3c', 2),
('海鲜', '/images/categories/seafood.png', '#3498db', 3),
('素食', '/images/categories/vegetarian.png', '#2ecc71', 4),
('汤品', '/images/categories/soup.png', '#f39c12', 5),
('甜品', '/images/categories/dessert.png', '#e91e63', 6),
('饮品', '/images/categories/drink.png', '#9c27b0', 7),
('主食', '/images/categories/staple.png', '#795548', 8);

-- 插入菜品数据
INSERT INTO dish (name, description, price, original_price, image, category_id, tags, rating, sales, is_hot, is_new, discount) VALUES
('宫保鸡丁', '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣', 28.00, 35.00, '/images/dishes/gongbao-chicken.jpg', 2, '["热门", "川菜", "下饭"]', 4.8, 1256, TRUE, FALSE, 0.8),
('麻婆豆腐', '正宗川味，豆腐嫩滑，麻辣鲜香', 22.00, 22.00, '/images/dishes/mapo-tofu.jpg', 4, '["素食", "川菜", "经典"]', 4.6, 892, TRUE, FALSE, 1.0),
('红烧肉', '肥而不腻，瘦而不柴，入口即化的经典家常菜', 35.00, 35.00, '/images/dishes/braised-pork.jpg', 2, '["家常菜", "红烧", "下饭"]', 4.9, 2103, TRUE, FALSE, 1.0),
('清蒸鲈鱼', '新鲜鲈鱼，肉质鲜嫩，营养丰富', 48.00, 48.00, '/images/dishes/steamed-fish.jpg', 3, '["海鲜", "清淡", "营养"]', 4.7, 567, FALSE, TRUE, 1.0),
('酸辣土豆丝', '爽脆可口，酸辣开胃，经典素菜', 15.00, 15.00, '/images/dishes/potato-strips.jpg', 4, '["素食", "开胃", "爽脆"]', 4.5, 1834, FALSE, FALSE, 1.0),
('糖醋里脊', '外酥内嫩，酸甜可口，老少皆宜', 32.00, 38.00, '/images/dishes/sweet-sour-pork.jpg', 2, '["酸甜", "油炸", "经典"]', 4.6, 743, FALSE, FALSE, 0.84),
('蒜蓉西兰花', '清淡健康，营养丰富，蒜香浓郁', 18.00, 18.00, '/images/dishes/garlic-broccoli.jpg', 4, '["素食", "健康", "清淡"]', 4.4, 456, FALSE, TRUE, 1.0),
('水煮鱼', '麻辣鲜香，鱼肉嫩滑，川菜经典', 58.00, 58.00, '/images/dishes/boiled-fish.jpg', 3, '["川菜", "麻辣", "鱼类"]', 4.8, 1089, TRUE, FALSE, 1.0);

-- 插入轮播图数据
INSERT INTO banner (title, subtitle, image, link, type, sort) VALUES
('新品上市', '精选美味，等你品尝', '/images/banners/banner1.jpg', '/pages/category/category?category=new', 'category', 1),
('限时优惠', '全场8折，机不可失', '/images/banners/banner2.jpg', '/pages/category/category?category=discount', 'promotion', 2),
('招牌菜品', '经典口味，传承美食', '/images/banners/banner3.jpg', '/pages/category/category?category=signature', 'category', 3);
