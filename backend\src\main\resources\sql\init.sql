-- 创建数据库
CREATE DATABASE IF NOT EXISTS `food_miniprogram` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `food_miniprogram`;

-- 菜品分类表
CREATE TABLE `category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `icon` varchar(255) DEFAULT NULL COMMENT '分类图标',
  `color` varchar(20) DEFAULT NULL COMMENT '分类颜色',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态（0-禁用，1-启用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品分类表';

-- 菜品表
CREATE TABLE `dish` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(100) NOT NULL COMMENT '菜品名称',
  `description` text COMMENT '菜品描述',
  `price` decimal(10,2) NOT NULL COMMENT '菜品价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `image` varchar(255) DEFAULT NULL COMMENT '主图片URL',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `tags` json DEFAULT NULL COMMENT '标签（JSON格式）',
  `rating` decimal(3,2) DEFAULT '0.00' COMMENT '评分',
  `sales` int DEFAULT '0' COMMENT '销量',
  `is_hot` tinyint DEFAULT '0' COMMENT '是否热门',
  `is_new` tinyint DEFAULT '0' COMMENT '是否新品',
  `is_recommend` tinyint DEFAULT '0' COMMENT '是否推荐',
  `discount` decimal(3,2) DEFAULT '1.00' COMMENT '折扣（0-1之间，1表示无折扣）',
  `stock` int DEFAULT '999' COMMENT '库存数量',
  `status` tinyint DEFAULT '1' COMMENT '状态（0-下架，1-上架）',
  `sort` int DEFAULT '0' COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_status` (`status`),
  KEY `idx_is_hot` (`is_hot`),
  KEY `idx_is_new` (`is_new`),
  KEY `idx_sort` (`sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品表';

-- 菜品图片表
CREATE TABLE `dish_image` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `dish_id` bigint NOT NULL COMMENT '菜品ID',
  `image_url` varchar(255) NOT NULL COMMENT '图片URL',
  `image_type` tinyint DEFAULT '1' COMMENT '图片类型（1-主图，2-详情图）',
  `sort` int DEFAULT '0' COMMENT '排序',
  `description` varchar(255) DEFAULT NULL COMMENT '图片描述',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_dish_id` (`dish_id`),
  KEY `idx_image_type` (`image_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='菜品图片表';

-- 轮播图表
CREATE TABLE `banner` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(100) NOT NULL COMMENT '标题',
  `subtitle` varchar(200) DEFAULT NULL COMMENT '副标题',
  `image` varchar(255) NOT NULL COMMENT '图片URL',
  `link` varchar(255) DEFAULT NULL COMMENT '跳转链接',
  `type` varchar(20) DEFAULT 'category' COMMENT '类型（category-分类，promotion-促销）',
  `sort` int DEFAULT '0' COMMENT '排序',
  `status` tinyint DEFAULT '1' COMMENT '状态（0-禁用，1-启用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_sort` (`sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='轮播图表';

-- 插入分类数据
INSERT INTO `category` (`name`, `icon`, `color`, `sort`) VALUES
('热门推荐', '/images/categories/hot.png', '#ff6b35', 1),
('肉类', '/images/categories/meat.png', '#e74c3c', 2),
('海鲜', '/images/categories/seafood.png', '#3498db', 3),
('素食', '/images/categories/vegetarian.png', '#2ecc71', 4),
('汤品', '/images/categories/soup.png', '#f39c12', 5),
('甜品', '/images/categories/dessert.png', '#e91e63', 6),
('饮品', '/images/categories/drink.png', '#9c27b0', 7),
('主食', '/images/categories/staple.png', '#795548', 8);

-- 插入菜品数据
INSERT INTO `dish` (`name`, `description`, `price`, `original_price`, `image`, `category_id`, `tags`, `rating`, `sales`, `is_hot`, `is_new`, `discount`) VALUES
('宫保鸡丁', '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣', 28.00, 35.00, '/images/dishes/gongbao-chicken.jpg', 2, '["热门", "川菜", "下饭"]', 4.8, 1256, 1, 0, 0.8),
('麻婆豆腐', '正宗川味，豆腐嫩滑，麻辣鲜香', 22.00, 22.00, '/images/dishes/mapo-tofu.jpg', 4, '["素食", "川菜", "经典"]', 4.6, 892, 1, 0, 1.0),
('红烧肉', '肥而不腻，瘦而不柴，入口即化的经典家常菜', 35.00, 35.00, '/images/dishes/braised-pork.jpg', 2, '["家常菜", "红烧", "下饭"]', 4.9, 2103, 1, 0, 1.0),
('清蒸鲈鱼', '新鲜鲈鱼，肉质鲜嫩，营养丰富', 48.00, 48.00, '/images/dishes/steamed-fish.jpg', 3, '["海鲜", "清淡", "营养"]', 4.7, 567, 0, 1, 1.0),
('酸辣土豆丝', '爽脆可口，酸辣开胃，经典素菜', 15.00, 15.00, '/images/dishes/potato-strips.jpg', 4, '["素食", "开胃", "爽脆"]', 4.5, 1834, 0, 0, 1.0),
('糖醋里脊', '外酥内嫩，酸甜可口，老少皆宜', 32.00, 38.00, '/images/dishes/sweet-sour-pork.jpg', 2, '["酸甜", "油炸", "经典"]', 4.6, 743, 0, 0, 0.84),
('蒜蓉西兰花', '清淡健康，营养丰富，蒜香浓郁', 18.00, 18.00, '/images/dishes/garlic-broccoli.jpg', 4, '["素食", "健康", "清淡"]', 4.4, 456, 0, 1, 1.0),
('水煮鱼', '麻辣鲜香，鱼肉嫩滑，川菜经典', 58.00, 58.00, '/images/dishes/boiled-fish.jpg', 3, '["川菜", "麻辣", "鱼类"]', 4.8, 1089, 1, 0, 1.0);

-- 插入轮播图数据
INSERT INTO `banner` (`title`, `subtitle`, `image`, `link`, `type`, `sort`) VALUES
('新品上市', '精选美味，等你品尝', '/images/banners/banner1.jpg', '/pages/category/category?category=new', 'category', 1),
('限时优惠', '全场8折，机不可失', '/images/banners/banner2.jpg', '/pages/category/category?category=discount', 'promotion', 2),
('招牌菜品', '经典口味，传承美食', '/images/banners/banner3.jpg', '/pages/category/category?category=signature', 'category', 3);
