<!--pages/profile/profile.wxml-->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-section">
    <view class="user-bg"></view>
    <view class="user-info" wx:if="{{isLogin}}">
      <image 
        class="avatar" 
        src="{{userInfo.avatar || '/images/default-avatar.png'}}" 
        mode="aspectFill"
      ></image>
      <view class="user-details">
        <text class="username">{{userInfo.nickname || userInfo.phone || '用户'}}</text>
        <text class="user-desc">{{userInfo.phone || ''}}</text>
      </view>
      <view class="user-actions">
        <button class="edit-btn" bindtap="onProfile">编辑</button>
      </view>
    </view>
    
    <view class="login-prompt" wx:else>
      <image class="login-avatar" src="/images/default-avatar.png" mode="aspectFill"></image>
      <view class="login-info">
        <text class="login-title">点击登录</text>
        <text class="login-desc">登录后享受更多服务</text>
      </view>
      <button class="login-btn" bindtap="onLogin">登录</button>
    </view>
  </view>

  <!-- 订单统计 -->
  <view class="order-stats" wx:if="{{isLogin}}">
    <view class="stats-title">我的订单</view>
    <view class="stats-list">
      <view class="stats-item" bindtap="onViewOrders" data-status="">
        <text class="stats-count">{{orderStats.pending + orderStats.confirmed + orderStats.completed}}</text>
        <text class="stats-label">全部订单</text>
      </view>
      <view class="stats-item" bindtap="onViewOrders" data-status="pending">
        <text class="stats-count">{{orderStats.pending}}</text>
        <text class="stats-label">待确认</text>
      </view>
      <view class="stats-item" bindtap="onViewOrders" data-status="confirmed">
        <text class="stats-count">{{orderStats.confirmed}}</text>
        <text class="stats-label">制作中</text>
      </view>
      <view class="stats-item" bindtap="onViewOrders" data-status="completed">
        <text class="stats-count">{{orderStats.completed}}</text>
        <text class="stats-label">已完成</text>
      </view>
    </view>
  </view>

  <!-- 功能菜单 -->
  <view class="menu-section">
    <!-- 个人服务 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="onProfile">
        <view class="menu-icon">👤</view>
        <text class="menu-title">个人信息</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onAddress">
        <view class="menu-icon">📍</view>
        <text class="menu-title">收货地址</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onCoupons">
        <view class="menu-icon">🎫</view>
        <text class="menu-title">优惠券</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 客服帮助 -->
    <view class="menu-group">
      <view class="menu-item" bindtap="onCustomerService">
        <view class="menu-icon">📞</view>
        <text class="menu-title">联系客服</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onAbout">
        <view class="menu-icon">ℹ️</view>
        <text class="menu-title">关于我们</text>
        <text class="menu-arrow">></text>
      </view>
      <view class="menu-item" bindtap="onSettings">
        <view class="menu-icon">⚙️</view>
        <text class="menu-title">设置</text>
        <text class="menu-arrow">></text>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="menu-group" wx:if="{{isLogin}}">
      <view class="menu-item logout" bindtap="onLogout">
        <view class="menu-icon">🚪</view>
        <text class="menu-title">退出登录</text>
        <text class="menu-arrow">></text>
      </view>
    </view>
  </view>
</view>
