<!--components/theme-selector/theme-selector.wxml-->
<view class="theme-selector {{show ? 'show' : 'hide'}}" wx:if="{{show}}">
  <!-- 遮罩层 -->
  <view class="mask" bindtap="onMaskTap"></view>
  
  <!-- 主题选择面板 -->
  <view class="theme-panel" animation="{{animationData}}" bindtap="onContentTap">
    <view class="panel-header">
      <text class="panel-title">选择主题</text>
      <view class="close-btn" bindtap="onClose">
        <text class="close-icon">✕</text>
      </view>
    </view>
    
    <view class="themes-grid">
      <view 
        class="theme-item {{currentTheme === theme.key ? 'active' : ''}}"
        wx:for="{{themes}}"
        wx:key="key"
        wx:for-item="theme"
        bindtap="onThemeSelect"
        data-theme="{{theme}}"
      >
        <!-- 主题预览 -->
        <view class="theme-preview" style="background: {{theme.primaryGradient}}">
          <view class="preview-circle" style="background: {{theme.secondary}}"></view>
          <view class="preview-bar" style="background: {{theme.success}}"></view>
          <view class="preview-dot" style="background: {{theme.warning}}"></view>
        </view>
        
        <!-- 主题名称 -->
        <text class="theme-name">{{theme.name}}</text>
        
        <!-- 选中标识 -->
        <view class="selected-indicator" wx:if="{{currentTheme === theme.key}}">
          <text class="selected-icon">✓</text>
        </view>
      </view>
    </view>
    
    <!-- 底部说明 -->
    <view class="panel-footer">
      <text class="footer-text">主题设置会自动保存</text>
    </view>
  </view>
</view>
