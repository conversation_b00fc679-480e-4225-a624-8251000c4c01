# 🚀 快速启动指南

## 🎉 后端服务已启动成功！

您的美食小程序后端服务现在正在运行中。

### 📍 服务地址
- **主页**: http://localhost:8080
- **API接口**: http://localhost:8080/api/
- **图片访问**: http://localhost:8080/images/

### 🔧 管理工具
- **状态监控**: [status.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/status.html)
- **图片上传**: [image-setup.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/image-setup.html)
- **图片生成**: [create-demo-images.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/create-demo-images.html)

### 📱 主要API接口

#### 1. 健康检查
```
GET http://localhost:8080/api/health
```

#### 2. 菜品相关
```
GET http://localhost:8080/api/dish/page     # 菜品列表
GET http://localhost:8080/api/dish/hot      # 热门菜品
```

#### 3. 分类和轮播图
```
GET http://localhost:8080/api/categories    # 分类列表
GET http://localhost:8080/api/banners       # 轮播图
```

#### 4. 文件上传
```
POST http://localhost:8080/api/file/upload  # 文件上传
```

### 🖼️ 添加图片资源

#### 方法一：使用图片生成工具
1. 打开 [create-demo-images.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/create-demo-images.html)
2. 点击"生成所有图片"
3. 右键保存需要的图片到对应文件夹

#### 方法二：使用图片上传工具
1. 打开 [image-setup.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/image-setup.html)
2. 拖拽或选择图片文件
3. 点击上传按钮

#### 图片存储路径
```
backend/uploads/
├── dishes/      # 菜品图片
├── banners/     # 轮播图
├── categories/  # 分类图标
├── avatars/     # 头像图片
└── temp/        # 临时文件
```

### 🔍 服务监控

打开 [status.html](file:///e:/微信小程序/微信小程序期末/miniprogram/backend/status.html) 可以实时监控：
- ✅ 服务运行状态
- ⏱️ API响应时间
- 📊 数据统计信息
- 🔗 各个接口的健康状态

### 📝 测试API

您可以使用以下方式测试API：

#### 1. 浏览器直接访问
```
http://localhost:8080/api/health
http://localhost:8080/api/dish/hot
```

#### 2. PowerShell命令
```powershell
Invoke-WebRequest -Uri "http://localhost:8080/api/health" | Select-Object -ExpandProperty Content
```

#### 3. curl命令
```bash
curl http://localhost:8080/api/health
```

### 🛠️ 服务管理

#### 停止服务
在运行服务的终端中按 `Ctrl + C`

#### 重启服务
```bash
cd backend
node demo-server.js
```

### 📱 连接小程序

在您的微信小程序中，将API基础地址设置为：
```javascript
const API_BASE = 'http://localhost:8080/api'
```

### 🎯 下一步

1. **添加图片资源**: 使用图片工具添加菜品、轮播图等图片
2. **测试API**: 确保所有接口正常工作
3. **连接小程序**: 在小程序中调用后端API
4. **数据管理**: 根据需要修改模拟数据

### ❓ 常见问题

**Q: 服务无法启动？**
A: 检查端口8080是否被占用，或修改demo-server.js中的PORT变量

**Q: 图片无法访问？**
A: 确保图片已上传到uploads目录，并检查文件路径

**Q: API返回错误？**
A: 查看服务器控制台日志，或使用状态监控页面检查

### 📞 技术支持

如有问题，请检查：
1. 服务器控制台输出
2. 状态监控页面
3. 浏览器开发者工具

---

🍽️ **美食小程序后端** - 让美食触手可及！
