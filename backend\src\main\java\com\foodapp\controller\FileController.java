package com.foodapp.controller;

import com.foodapp.common.Result;
import com.foodapp.service.FileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 文件上传控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/file")
@CrossOrigin(origins = "*")
public class FileController {

    @Autowired
    private FileService fileService;

    /**
     * 单文件上传
     */
    @PostMapping("/upload")
    public Result<Map<String, Object>> uploadFile(@RequestParam("file") MultipartFile file,
                                                  @RequestParam(value = "type", defaultValue = "image") String type) {
        try {
            log.info("开始上传文件: {}, 大小: {} bytes", file.getOriginalFilename(), file.getSize());
            
            Map<String, Object> result = fileService.uploadFile(file, type);
            
            log.info("文件上传成功: {}", result.get("url"));
            return Result.success(result);
        } catch (Exception e) {
            log.error("文件上传失败", e);
            return Result.error("文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 多文件上传
     */
    @PostMapping("/upload/batch")
    public Result<List<Map<String, Object>>> uploadFiles(@RequestParam("files") MultipartFile[] files,
                                                         @RequestParam(value = "type", defaultValue = "image") String type) {
        try {
            log.info("开始批量上传文件，数量: {}", files.length);
            
            List<Map<String, Object>> results = fileService.uploadFiles(files, type);
            
            log.info("批量文件上传成功，成功数量: {}", results.size());
            return Result.success(results);
        } catch (Exception e) {
            log.error("批量文件上传失败", e);
            return Result.error("批量文件上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public Result<Void> deleteFile(@RequestParam("url") String url) {
        try {
            log.info("开始删除文件: {}", url);
            
            fileService.deleteFile(url);
            
            log.info("文件删除成功: {}", url);
            return Result.success();
        } catch (Exception e) {
            log.error("文件删除失败", e);
            return Result.error("文件删除失败: " + e.getMessage());
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/info")
    public Result<Map<String, Object>> getFileInfo(@RequestParam("url") String url) {
        try {
            Map<String, Object> info = fileService.getFileInfo(url);
            return Result.success(info);
        } catch (Exception e) {
            log.error("获取文件信息失败", e);
            return Result.error("获取文件信息失败: " + e.getMessage());
        }
    }

    /**
     * 图片压缩
     */
    @PostMapping("/compress")
    public Result<Map<String, Object>> compressImage(@RequestParam("file") MultipartFile file,
                                                     @RequestParam(value = "quality", defaultValue = "0.8") float quality,
                                                     @RequestParam(value = "width", required = false) Integer width,
                                                     @RequestParam(value = "height", required = false) Integer height) {
        try {
            log.info("开始压缩图片: {}, 质量: {}", file.getOriginalFilename(), quality);
            
            Map<String, Object> result = fileService.compressImage(file, quality, width, height);
            
            log.info("图片压缩成功: {}", result.get("url"));
            return Result.success(result);
        } catch (Exception e) {
            log.error("图片压缩失败", e);
            return Result.error("图片压缩失败: " + e.getMessage());
        }
    }

    /**
     * 生成缩略图
     */
    @PostMapping("/thumbnail")
    public Result<Map<String, Object>> generateThumbnail(@RequestParam("file") MultipartFile file,
                                                         @RequestParam(value = "width", defaultValue = "200") int width,
                                                         @RequestParam(value = "height", defaultValue = "200") int height) {
        try {
            log.info("开始生成缩略图: {}, 尺寸: {}x{}", file.getOriginalFilename(), width, height);
            
            Map<String, Object> result = fileService.generateThumbnail(file, width, height);
            
            log.info("缩略图生成成功: {}", result.get("url"));
            return Result.success(result);
        } catch (Exception e) {
            log.error("缩略图生成失败", e);
            return Result.error("缩略图生成失败: " + e.getMessage());
        }
    }

    /**
     * 获取上传配置
     */
    @GetMapping("/config")
    public Result<Map<String, Object>> getUploadConfig() {
        try {
            Map<String, Object> config = fileService.getUploadConfig();
            return Result.success(config);
        } catch (Exception e) {
            log.error("获取上传配置失败", e);
            return Result.error("获取上传配置失败: " + e.getMessage());
        }
    }
}
