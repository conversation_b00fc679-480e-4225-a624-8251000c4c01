<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试小程序后端连接</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 10px;
            background: #f9f9f9;
        }
        .test-button {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #e3f2fd;
            color: #1976d2;
            border: 1px solid #bbdefb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 小程序后端连接测试</h1>
        
        <div class="test-section">
            <h3>📡 基础连接测试</h3>
            <button class="test-button" onclick="testConnection()">测试连接</button>
            <button class="test-button" onclick="testHealth()">健康检查</button>
            <div id="connectionResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>🍽️ 菜品API测试</h3>
            <button class="test-button" onclick="testHotDishes()">热门菜品</button>
            <button class="test-button" onclick="testNewDishes()">新品推荐</button>
            <button class="test-button" onclick="testDishPage()">菜品分页</button>
            <div id="dishResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>📂 分类和轮播图测试</h3>
            <button class="test-button" onclick="testCategories()">分类列表</button>
            <button class="test-button" onclick="testBanners()">轮播图</button>
            <div id="categoryResult" class="result info">点击按钮开始测试...</div>
        </div>

        <div class="test-section">
            <h3>📱 小程序配置</h3>
            <p><strong>当前配置的API地址:</strong> <code>http://localhost:8080/api</code></p>
            <p><strong>图片访问地址:</strong> <code>http://localhost:8080/images</code></p>
            <div class="result info">
如果小程序无法连接，请检查：
1. 微信开发者工具是否开启了"不校验合法域名"
2. 后端服务是否正在运行
3. 端口8080是否被占用
4. 防火墙是否阻止了连接
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';

        async function makeRequest(url, resultElementId) {
            const resultEl = document.getElementById(resultElementId);
            resultEl.textContent = '请求中...';
            resultEl.className = 'result info';

            try {
                const startTime = Date.now();
                const response = await fetch(url);
                const endTime = Date.now();
                const data = await response.json();

                if (response.ok) {
                    resultEl.className = 'result success';
                    resultEl.textContent = `✅ 请求成功 (${endTime - startTime}ms)\n\n${JSON.stringify(data, null, 2)}`;
                } else {
                    resultEl.className = 'result error';
                    resultEl.textContent = `❌ 请求失败 (${response.status})\n\n${JSON.stringify(data, null, 2)}`;
                }
            } catch (error) {
                resultEl.className = 'result error';
                resultEl.textContent = `❌ 连接失败: ${error.message}\n\n请检查后端服务是否正在运行`;
            }
        }

        function testConnection() {
            makeRequest(`${API_BASE}/`, 'connectionResult');
        }

        function testHealth() {
            makeRequest(`${API_BASE}/api/health`, 'connectionResult');
        }

        function testHotDishes() {
            makeRequest(`${API_BASE}/api/dish/hot`, 'dishResult');
        }

        function testNewDishes() {
            makeRequest(`${API_BASE}/api/dish/new`, 'dishResult');
        }

        function testDishPage() {
            makeRequest(`${API_BASE}/api/dish/page`, 'dishResult');
        }

        function testCategories() {
            makeRequest(`${API_BASE}/api/categories`, 'categoryResult');
        }

        function testBanners() {
            makeRequest(`${API_BASE}/api/banners`, 'categoryResult');
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            testConnection();
        };
    </script>
</body>
</html>
