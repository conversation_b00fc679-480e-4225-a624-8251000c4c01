/* components/floating-cart/floating-cart.wxss */
.floating-cart {
  position: fixed;
  bottom: 120rpx;
  left: 30rpx;
  right: 30rpx;
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.floating-cart.show {
  transform: translateY(0);
  opacity: 1;
}

.floating-cart.hide {
  transform: translateY(100rpx);
  opacity: 0;
}

.cart-content {
  position: relative;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 8rpx 30rpx rgba(255, 107, 53, 0.4);
  backdrop-filter: blur(20rpx);
  overflow: hidden;
}

.cart-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

.cart-icon-wrapper {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  position: relative;
  width: 80rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.cart-icon .icon {
  font-size: 36rpx;
  color: white;
}

.cart-badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  min-width: 36rpx;
  height: 36rpx;
  background: #ff4757;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid white;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10rpx rgba(255, 71, 87, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
  }
}

.badge-text {
  font-size: 20rpx;
  color: white;
  font-weight: bold;
  line-height: 1;
}

.price-info {
  flex: 1;
  margin-left: 20rpx;
}

.price-label {
  display: block;
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 4rpx;
}

.price-value {
  display: block;
  font-size: 36rpx;
  color: white;
  font-weight: bold;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.checkout-btn {
  background: rgba(255, 255, 255, 0.9);
  color: #ff6b35;
  padding: 20rpx 40rpx;
  border-radius: 40rpx;
  font-weight: bold;
  font-size: 28rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.checkout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 107, 53, 0.1), transparent);
  transition: left 0.5s;
}

.checkout-btn:active::before {
  left: 100%;
}

.checkout-btn:active {
  transform: scale(0.95);
  background: rgba(255, 255, 255, 1);
}

.checkout-text {
  position: relative;
  z-index: 1;
}

.glow-effect {
  position: absolute;
  top: -20rpx;
  left: -20rpx;
  right: -20rpx;
  bottom: -20rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 60rpx;
  opacity: 0.3;
  filter: blur(20rpx);
  z-index: -1;
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    opacity: 0.3;
    transform: scale(1);
  }
  to {
    opacity: 0.6;
    transform: scale(1.05);
  }
}
