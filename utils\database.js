// utils/database.js - 本地数据库管理
class Database {
  constructor() {
    this.storageKeys = {
      dishes: 'dishes_data',
      categories: 'categories_data',
      banners: 'banners_data',
      orders: 'orders_data',
      favorites: 'favorites_data',
      reviews: 'reviews_data'
    }
    this.initDatabase()
  }

  // 初始化数据库
  initDatabase() {
    // 检查是否已有数据，如果没有则初始化
    if (!this.getData('dishes')) {
      this.initDishes()
    }
    if (!this.getData('categories')) {
      this.initCategories()
    }
    if (!this.getData('banners')) {
      this.initBanners()
    }
  }

  // 通用数据存储方法
  setData(key, data) {
    try {
      wx.setStorageSync(this.storageKeys[key] || key, data)
      return true
    } catch (error) {
      console.error('数据存储失败:', error)
      return false
    }
  }

  // 通用数据获取方法
  getData(key) {
    try {
      return wx.getStorageSync(this.storageKeys[key] || key) || null
    } catch (error) {
      console.error('数据获取失败:', error)
      return null
    }
  }

  // 初始化菜品数据
  initDishes() {
    const dishes = [
      {
        id: 1,
        name: '宫保鸡丁',
        description: '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣',
        price: 28.00,
        originalPrice: 35.00,
        image: '/images/dishes/gongbao-chicken.jpg',
        category: 'hot',
        tags: ['热门', '川菜', '下饭'],
        rating: 4.8,
        sales: 1256,
        isHot: true,
        isNew: false,
        discount: 0.8
      },
      {
        id: 2,
        name: '麻婆豆腐',
        description: '正宗川味，豆腐嫩滑，麻辣鲜香',
        price: 22.00,
        originalPrice: 22.00,
        image: '/images/dishes/mapo-tofu.jpg',
        category: 'vegetarian',
        tags: ['素食', '川菜', '经典'],
        rating: 4.6,
        sales: 892,
        isHot: true,
        isNew: false
      },
      {
        id: 3,
        name: '红烧肉',
        description: '肥而不腻，瘦而不柴，入口即化的经典家常菜',
        price: 35.00,
        originalPrice: 35.00,
        image: '/images/dishes/braised-pork.jpg',
        category: 'meat',
        tags: ['家常菜', '红烧', '下饭'],
        rating: 4.9,
        sales: 2103,
        isHot: true,
        isNew: false
      },
      {
        id: 4,
        name: '清蒸鲈鱼',
        description: '新鲜鲈鱼，肉质鲜嫩，营养丰富',
        price: 48.00,
        originalPrice: 48.00,
        image: '/images/dishes/steamed-fish.jpg',
        category: 'seafood',
        tags: ['海鲜', '清淡', '营养'],
        rating: 4.7,
        sales: 567,
        isHot: false,
        isNew: true
      },
      {
        id: 5,
        name: '酸辣土豆丝',
        description: '爽脆可口，酸辣开胃，经典素菜',
        price: 15.00,
        originalPrice: 15.00,
        image: '/images/dishes/potato-strips.jpg',
        category: 'vegetarian',
        tags: ['素食', '开胃', '爽脆'],
        rating: 4.5,
        sales: 1834,
        isHot: false,
        isNew: false
      },
      {
        id: 6,
        name: '糖醋里脊',
        description: '外酥内嫩，酸甜可口，老少皆宜',
        price: 32.00,
        originalPrice: 38.00,
        image: '/images/dishes/sweet-sour-pork.jpg',
        category: 'meat',
        tags: ['酸甜', '油炸', '经典'],
        rating: 4.6,
        sales: 743,
        isHot: false,
        isNew: false,
        discount: 0.84
      },
      {
        id: 7,
        name: '蒜蓉西兰花',
        description: '清淡健康，营养丰富，蒜香浓郁',
        price: 18.00,
        originalPrice: 18.00,
        image: '/images/dishes/garlic-broccoli.jpg',
        category: 'vegetarian',
        tags: ['素食', '健康', '清淡'],
        rating: 4.4,
        sales: 456,
        isHot: false,
        isNew: true
      },
      {
        id: 8,
        name: '水煮鱼',
        description: '麻辣鲜香，鱼肉嫩滑，川菜经典',
        price: 58.00,
        originalPrice: 58.00,
        image: '/images/dishes/boiled-fish.jpg',
        category: 'seafood',
        tags: ['川菜', '麻辣', '鱼类'],
        rating: 4.8,
        sales: 1089,
        isHot: true,
        isNew: false
      }
    ]
    this.setData('dishes', dishes)
  }

  // 初始化分类数据
  initCategories() {
    const categories = [
      {
        id: 1,
        name: '热门推荐',
        key: 'hot',
        icon: '/images/categories/hot.png',
        color: '#ff6b35'
      },
      {
        id: 2,
        name: '肉类',
        key: 'meat',
        icon: '/images/categories/meat.png',
        color: '#e74c3c'
      },
      {
        id: 3,
        name: '海鲜',
        key: 'seafood',
        icon: '/images/categories/seafood.png',
        color: '#3498db'
      },
      {
        id: 4,
        name: '素食',
        key: 'vegetarian',
        icon: '/images/categories/vegetarian.png',
        color: '#2ecc71'
      },
      {
        id: 5,
        name: '汤品',
        key: 'soup',
        icon: '/images/categories/soup.png',
        color: '#f39c12'
      },
      {
        id: 6,
        name: '甜品',
        key: 'dessert',
        icon: '/images/categories/dessert.png',
        color: '#e91e63'
      },
      {
        id: 7,
        name: '饮品',
        key: 'drink',
        icon: '/images/categories/drink.png',
        color: '#9c27b0'
      },
      {
        id: 8,
        name: '主食',
        key: 'staple',
        icon: '/images/categories/staple.png',
        color: '#795548'
      }
    ]
    this.setData('categories', categories)
  }

  // 初始化轮播图数据
  initBanners() {
    const banners = [
      {
        id: 1,
        title: '新品上市',
        subtitle: '精选美味，等你品尝',
        image: '/images/banners/banner1.jpg',
        link: '/pages/category/category?category=new',
        type: 'category'
      },
      {
        id: 2,
        title: '限时优惠',
        subtitle: '全场8折，机不可失',
        image: '/images/banners/banner2.jpg',
        link: '/pages/category/category?category=discount',
        type: 'promotion'
      },
      {
        id: 3,
        title: '招牌菜品',
        subtitle: '经典口味，传承美食',
        image: '/images/banners/banner3.jpg',
        link: '/pages/category/category?category=signature',
        type: 'category'
      }
    ]
    this.setData('banners', banners)
  }

  // 获取菜品列表
  getDishes(options = {}) {
    const dishes = this.getData('dishes') || []
    let result = [...dishes]

    // 按分类筛选
    if (options.category && options.category !== 'all') {
      if (options.category === 'hot') {
        result = result.filter(dish => dish.isHot)
      } else if (options.category === 'new') {
        result = result.filter(dish => dish.isNew)
      } else {
        result = result.filter(dish => dish.category === options.category)
      }
    }

    // 搜索
    if (options.keyword) {
      const keyword = options.keyword.toLowerCase()
      result = result.filter(dish => 
        dish.name.toLowerCase().includes(keyword) ||
        dish.description.toLowerCase().includes(keyword) ||
        dish.tags.some(tag => tag.toLowerCase().includes(keyword))
      )
    }

    // 排序
    if (options.sortBy) {
      switch (options.sortBy) {
        case 'price_asc':
          result.sort((a, b) => a.price - b.price)
          break
        case 'price_desc':
          result.sort((a, b) => b.price - a.price)
          break
        case 'rating':
          result.sort((a, b) => b.rating - a.rating)
          break
        case 'sales':
          result.sort((a, b) => b.sales - a.sales)
          break
        default:
          // 默认排序：热门 > 新品 > 评分
          result.sort((a, b) => {
            if (a.isHot !== b.isHot) return b.isHot - a.isHot
            if (a.isNew !== b.isNew) return b.isNew - a.isNew
            return b.rating - a.rating
          })
      }
    }

    // 分页
    if (options.page && options.pageSize) {
      const start = (options.page - 1) * options.pageSize
      const end = start + options.pageSize
      result = result.slice(start, end)
    }

    return result
  }

  // 获取单个菜品
  getDish(id) {
    const dishes = this.getData('dishes') || []
    return dishes.find(dish => dish.id === parseInt(id))
  }

  // 获取分类列表
  getCategories() {
    return this.getData('categories') || []
  }

  // 获取轮播图
  getBanners() {
    return this.getData('banners') || []
  }

  // 获取热门菜品
  getHotDishes(limit = 10) {
    return this.getDishes({ category: 'hot' }).slice(0, limit)
  }

  // 获取新品推荐
  getNewDishes(limit = 10) {
    return this.getDishes({ category: 'new' }).slice(0, limit)
  }

  // 添加收藏
  addFavorite(dishId) {
    const favorites = this.getData('favorites') || []
    if (!favorites.includes(dishId)) {
      favorites.push(dishId)
      this.setData('favorites', favorites)
    }
    return true
  }

  // 移除收藏
  removeFavorite(dishId) {
    const favorites = this.getData('favorites') || []
    const index = favorites.indexOf(dishId)
    if (index > -1) {
      favorites.splice(index, 1)
      this.setData('favorites', favorites)
    }
    return true
  }

  // 检查是否已收藏
  isFavorite(dishId) {
    const favorites = this.getData('favorites') || []
    return favorites.includes(dishId)
  }

  // 获取收藏列表
  getFavorites() {
    const favorites = this.getData('favorites') || []
    const dishes = this.getData('dishes') || []
    return dishes.filter(dish => favorites.includes(dish.id))
  }

  // 清空所有数据
  clearAll() {
    Object.values(this.storageKeys).forEach(key => {
      wx.removeStorageSync(key)
    })
  }

  // 重置数据库
  reset() {
    this.clearAll()
    this.initDatabase()
  }
}

// 创建全局数据库实例
const database = new Database()

module.exports = {
  database,
  Database
}
