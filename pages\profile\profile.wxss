/* pages/profile/profile.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 用户信息区域 */
.user-section {
  position: relative;
  background-color: white;
  margin-bottom: 20rpx;
}

.user-bg {
  height: 300rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
}

.user-info, .login-prompt {
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  transform: translateY(-50%);
  display: flex;
  align-items: center;
  padding: 0 40rpx;
}

.avatar, .login-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  margin-right: 30rpx;
}

.user-details, .login-info {
  flex: 1;
}

.username, .login-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 10rpx;
}

.user-desc, .login-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.edit-btn, .login-btn {
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  padding: 15rpx 30rpx;
  font-size: 24rpx;
}

/* 订单统计 */
.order-stats {
  background-color: white;
  margin-bottom: 20rpx;
  padding: 30rpx;
}

.stats-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
}

.stats-list {
  display: flex;
  justify-content: space-between;
}

.stats-item {
  flex: 1;
  text-align: center;
  padding: 20rpx 0;
}

.stats-count {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
  margin-bottom: 10rpx;
}

.stats-label {
  display: block;
  font-size: 24rpx;
  color: #666;
}

/* 功能菜单 */
.menu-section {
  padding: 0 20rpx;
}

.menu-group {
  background-color: white;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item.logout {
  color: #ff4757;
}

.menu-icon {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  margin-right: 30rpx;
}

.menu-title {
  flex: 1;
  font-size: 28rpx;
  color: #333;
}

.menu-item.logout .menu-title {
  color: #ff4757;
}

.menu-arrow {
  font-size: 24rpx;
  color: #999;
}
