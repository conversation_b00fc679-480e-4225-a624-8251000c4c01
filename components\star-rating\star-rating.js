// components/star-rating/star-rating.js
Component({
  properties: {
    rating: {
      type: Number,
      value: 0
    },
    maxRating: {
      type: Number,
      value: 5
    },
    size: {
      type: String,
      value: 'medium' // small, medium, large
    },
    readonly: {
      type: Boolean,
      value: true
    },
    showText: {
      type: Boolean,
      value: true
    },
    animated: {
      type: Boolean,
      value: true
    }
  },

  data: {
    stars: [],
    currentRating: 0
  },

  observers: {
    'rating, maxRating': function(rating, maxRating) {
      this.updateStars(rating, maxRating)
    }
  },

  methods: {
    updateStars(rating, maxRating) {
      const stars = []
      const fullStars = Math.floor(rating)
      const hasHalfStar = rating % 1 >= 0.5
      
      for (let i = 0; i < maxRating; i++) {
        if (i < fullStars) {
          stars.push({ type: 'full', index: i })
        } else if (i === fullStars && hasHalfStar) {
          stars.push({ type: 'half', index: i })
        } else {
          stars.push({ type: 'empty', index: i })
        }
      }
      
      this.setData({ 
        stars,
        currentRating: rating
      })

      if (this.data.animated) {
        this.playAnimation()
      }
    },

    playAnimation() {
      const { stars } = this.data
      stars.forEach((star, index) => {
        setTimeout(() => {
          const animation = wx.createAnimation({
            duration: 300,
            timingFunction: 'ease-out'
          })
          
          animation.scale(1.3).step({ duration: 150 })
          animation.scale(1).step({ duration: 150 })
          
          this.setData({
            [`stars[${index}].animation`]: animation.export()
          })
        }, index * 100)
      })
    },

    onStarTap(e) {
      if (this.data.readonly) return
      
      const { index } = e.currentTarget.dataset
      const newRating = index + 1
      
      this.setData({ currentRating: newRating })
      this.updateStars(newRating, this.data.maxRating)
      
      this.triggerEvent('ratingChange', { 
        rating: newRating 
      })
    },

    getRatingText(rating) {
      if (rating >= 4.5) return '非常好'
      if (rating >= 4.0) return '很好'
      if (rating >= 3.5) return '好'
      if (rating >= 3.0) return '一般'
      if (rating >= 2.0) return '较差'
      return '很差'
    }
  }
})
