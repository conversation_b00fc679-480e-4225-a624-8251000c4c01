/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --primary-color: #ff6b35;
  --primary-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --primary-gradient-hover: linear-gradient(135deg, #ff7849 0%, #fa9d32 100%);
  --secondary-color: #667eea;
  --secondary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --success-color: #2ed573;
  --success-gradient: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
  --warning-color: #ffa502;
  --warning-gradient: linear-gradient(135deg, #ffa502 0%, #ff6348 100%);
  --danger-color: #ff4757;
  --danger-gradient: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  --text-color: #2f3542;
  --text-light: #57606f;
  --text-white: #ffffff;
  --border-color: #ddd;
  --shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  --shadow-large: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  --shadow-colored: 0 8rpx 32rpx rgba(255, 107, 53, 0.3);
  --border-radius: 16rpx;
  --border-radius-large: 24rpx;
  --border-radius-xl: 32rpx;
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --backdrop-blur: blur(20rpx);
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--border-radius);
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.btn:active::before {
  left: 100%;
}

.btn::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.btn:active::after {
  width: 300rpx;
  height: 300rpx;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: var(--shadow-colored);
}

.btn-primary:hover {
  background: var(--primary-gradient-hover);
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 40rpx rgba(255, 107, 53, 0.4);
}

.btn-primary:active {
  transform: translateY(1rpx);
  box-shadow: 0 4rpx 20rpx rgba(255, 107, 53, 0.3);
}

.btn-secondary {
  background: var(--secondary-gradient);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 25rpx rgba(102, 126, 234, 0.4);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  backdrop-filter: var(--backdrop-blur);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-2rpx);
}

.btn-ghost {
  background: var(--glass-bg);
  color: white;
  border: 2rpx solid var(--glass-border);
  backdrop-filter: var(--backdrop-blur);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2rpx);
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 32rpx;
  border-radius: var(--border-radius-large);
}

.btn-xl {
  padding: 40rpx 80rpx;
  font-size: 36rpx;
  border-radius: var(--border-radius-xl);
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: var(--backdrop-blur);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow);
  margin: 20rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 1rpx solid var(--glass-border);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2rpx;
  background: var(--primary-gradient);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.card:hover {
  transform: translateY(-6rpx);
  box-shadow: var(--shadow-large);
}

.card:hover::before {
  opacity: 1;
}

.card-glass {
  background: var(--glass-bg);
  backdrop-filter: var(--backdrop-blur);
  border: 1rpx solid var(--glass-border);
}

.card-glass:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.card-gradient {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: var(--backdrop-blur);
}

.card-premium {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 248, 240, 0.95) 100%);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  box-shadow: var(--shadow-colored);
}

.card-premium::before {
  background: var(--primary-gradient);
  opacity: 1;
  height: 4rpx;
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
  font-size: 32rpx;
  color: var(--text-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background: rgba(248, 249, 250, 0.5);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  margin-left: 20rpx;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.list-item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.list-item-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 图片样式 */
.image-round {
  border-radius: 50%;
}

.image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 文本样式 */
.text-primary {
  color: #ff6b35;
}

.text-secondary {
  color: #6c757d;
}

.text-success {
  color: #28a745;
}

.text-danger {
  color: #dc3545;
}

.text-warning {
  color: #ffc107;
}

.text-muted {
  color: #6c757d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 20rpx; }
.m-2 { margin: 40rpx; }
.m-3 { margin: 60rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 20rpx; }
.mt-2 { margin-top: 40rpx; }
.mt-3 { margin-top: 60rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 20rpx; }
.mb-2 { margin-bottom: 40rpx; }
.mb-3 { margin-bottom: 60rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 20rpx; }
.p-2 { padding: 40rpx; }
.p-3 { padding: 60rpx; }

/* 布局样式 */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 加载样式 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 20rpx;
  background-color: #ff6b35;
  min-width: 20rpx;
}

.badge-dot {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ff4757;
  animation: pulse 2s infinite;
}

/* 动画效果 */
@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0.7);
  }
  70% {
    transform: scale(1.1);
    box-shadow: 0 0 0 10rpx rgba(255, 71, 87, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(255, 71, 87, 0);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-60rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-60rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(60rpx);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30rpx, 0);
  }
  70% {
    transform: translate3d(0, -15rpx, 0);
  }
  90% {
    transform: translate3d(0, -4rpx, 0);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10rpx);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 20rpx rgba(255, 107, 53, 0.3);
  }
  50% {
    box-shadow: 0 0 40rpx rgba(255, 107, 53, 0.6);
  }
}

/* 动画类 */
.animate-fadeInUp {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInDown {
  animation: fadeInDown 0.6s ease-out;
}

.animate-fadeInLeft {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
  animation: fadeInRight 0.6s ease-out;
}

.animate-scaleIn {
  animation: scaleIn 0.4s ease-out;
}

.animate-bounce {
  animation: bounce 1s ease-in-out;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite;
}

/* 延迟动画 */
.animate-delay-1 { animation-delay: 0.1s; }
.animate-delay-2 { animation-delay: 0.2s; }
.animate-delay-3 { animation-delay: 0.3s; }
.animate-delay-4 { animation-delay: 0.4s; }
.animate-delay-5 { animation-delay: 0.5s; }

/* 粒子背景效果 */
.particles-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
}

.particle {
  position: absolute;
  width: 4rpx;
  height: 4rpx;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) { left: 10%; animation-delay: 0s; }
.particle:nth-child(2) { left: 20%; animation-delay: 1s; }
.particle:nth-child(3) { left: 30%; animation-delay: 2s; }
.particle:nth-child(4) { left: 40%; animation-delay: 3s; }
.particle:nth-child(5) { left: 50%; animation-delay: 4s; }
.particle:nth-child(6) { left: 60%; animation-delay: 5s; }
.particle:nth-child(7) { left: 70%; animation-delay: 0.5s; }
.particle:nth-child(8) { left: 80%; animation-delay: 1.5s; }
.particle:nth-child(9) { left: 90%; animation-delay: 2.5s; }

@keyframes float {
  0%, 100% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100rpx) rotate(360deg);
    opacity: 0;
  }
}

/* 渐变文字效果 */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-secondary {
  background: var(--secondary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.gradient-text-success {
  background: var(--success-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}
