/**app.wxss**/
.container {
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 200rpx 0;
  box-sizing: border-box;
}

/* 全局样式 */
page {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  --primary-color: #ff6b35;
  --primary-gradient: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  --secondary-color: #667eea;
  --success-color: #2ed573;
  --warning-color: #ffa502;
  --danger-color: #ff4757;
  --text-color: #2f3542;
  --text-light: #57606f;
  --border-color: #ddd;
  --shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  --shadow-hover: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  --border-radius: 16rpx;
  --border-radius-large: 24rpx;
}

/* 通用按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: var(--border-radius);
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow);
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn:active::before {
  left: 100%;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(255, 107, 53, 0.3);
}

.btn-primary:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(255, 107, 53, 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, #5a67d8 100%);
  color: white;
  box-shadow: 0 4rpx 15rpx rgba(102, 126, 234, 0.3);
}

.btn-outline {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
  backdrop-filter: blur(10rpx);
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10rpx);
}

.btn-small {
  padding: 16rpx 32rpx;
  font-size: 24rpx;
}

.btn-large {
  padding: 32rpx 64rpx;
  font-size: 32rpx;
  border-radius: var(--border-radius-large);
}

/* 卡片样式 */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow);
  margin: 20rpx;
  overflow: hidden;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card:hover {
  transform: translateY(-4rpx);
  box-shadow: var(--shadow-hover);
}

.card-glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.card-gradient {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  backdrop-filter: blur(20rpx);
}

.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  font-weight: 600;
  font-size: 32rpx;
  color: var(--text-color);
}

.card-body {
  padding: 30rpx;
}

.card-footer {
  padding: 20rpx 30rpx;
  background: rgba(248, 249, 250, 0.5);
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

/* 列表样式 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-content {
  flex: 1;
  margin-left: 20rpx;
}

.list-item-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 10rpx;
}

.list-item-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.4;
}

.list-item-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 图片样式 */
.image-round {
  border-radius: 50%;
}

.image-cover {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 文本样式 */
.text-primary {
  color: #ff6b35;
}

.text-secondary {
  color: #6c757d;
}

.text-success {
  color: #28a745;
}

.text-danger {
  color: #dc3545;
}

.text-warning {
  color: #ffc107;
}

.text-muted {
  color: #6c757d;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-bold {
  font-weight: bold;
}

/* 间距样式 */
.m-0 { margin: 0; }
.m-1 { margin: 20rpx; }
.m-2 { margin: 40rpx; }
.m-3 { margin: 60rpx; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 20rpx; }
.mt-2 { margin-top: 40rpx; }
.mt-3 { margin-top: 60rpx; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 20rpx; }
.mb-2 { margin-bottom: 40rpx; }
.mb-3 { margin-bottom: 60rpx; }

.p-0 { padding: 0; }
.p-1 { padding: 20rpx; }
.p-2 { padding: 40rpx; }
.p-3 { padding: 60rpx; }

/* 布局样式 */
.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.align-items-center {
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 加载样式 */
.loading {
  text-align: center;
  padding: 40rpx;
  color: #999;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 40rpx;
}

/* 徽章样式 */
.badge {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  line-height: 1;
  color: white;
  text-align: center;
  white-space: nowrap;
  vertical-align: baseline;
  border-radius: 20rpx;
  background-color: #ff6b35;
  min-width: 20rpx;
}

.badge-dot {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background-color: #ff4757;
}
