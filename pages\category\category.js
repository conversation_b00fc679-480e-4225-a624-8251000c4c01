// pages/category/category.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, getImageUrl } = require('../../utils/util')

Page({
  data: {
    categories: [],
    currentCategoryId: null,
    dishes: [],
    loading: true,
    searchValue: '',
    page: 1,
    hasMore: true
  },

  onLoad(options) {
    const { categoryId, type } = options
    
    if (categoryId) {
      this.setData({ currentCategoryId: parseInt(categoryId) })
    }
    
    this.loadCategories().then(() => {
      if (type === 'hot') {
        this.loadHotDishes()
      } else if (type === 'new') {
        this.loadNewDishes()
      } else if (categoryId) {
        this.loadDishesByCategory(categoryId)
      } else {
        this.loadAllDishes()
      }
    })
  },

  onShow() {
    this.updateCartCount()
  },

  onPullDownRefresh() {
    this.setData({
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    if (this.data.currentCategoryId) {
      this.loadDishesByCategory(this.data.currentCategoryId)
    } else {
      this.loadAllDishes()
    }
    
    wx.stopPullDownRefresh()
  },

  onReachBottom() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreDishes()
    }
  },

  // 加载分类列表
  async loadCategories() {
    try {
      const res = await get('/categories')
      this.setData({
        categories: res.data || []
      })
    } catch (error) {
      console.error('加载分类失败:', error)
    }
  },

  // 加载所有菜品
  async loadAllDishes() {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get('/dishes', {
        page: this.data.page,
        limit: 20
      })
      
      this.setData({
        dishes: this.data.page === 1 ? res.data.list : [...this.data.dishes, ...res.data.list],
        hasMore: res.data.pagination.page < res.data.pagination.pages,
        loading: false
      })
    } catch (error) {
      console.error('加载菜品失败:', error)
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  },

  // 按分类加载菜品
  async loadDishesByCategory(categoryId) {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get(`/categories/${categoryId}/dishes`, {
        page: this.data.page,
        limit: 20
      })
      
      this.setData({
        dishes: this.data.page === 1 ? res.data.list : [...this.data.dishes, ...res.data.list],
        hasMore: res.data.pagination.page < res.data.pagination.pages,
        loading: false
      })
    } catch (error) {
      console.error('加载分类菜品失败:', error)
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  },

  // 加载热门菜品
  async loadHotDishes() {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get('/dishes/hot', { limit: 50 })
      
      this.setData({
        dishes: res.data || [],
        hasMore: false,
        loading: false
      })
      
      wx.setNavigationBarTitle({
        title: '热门菜品'
      })
    } catch (error) {
      console.error('加载热门菜品失败:', error)
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  },

  // 加载新品推荐
  async loadNewDishes() {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get('/dishes/new', { limit: 50 })
      
      this.setData({
        dishes: res.data || [],
        hasMore: false,
        loading: false
      })
      
      wx.setNavigationBarTitle({
        title: '新品推荐'
      })
    } catch (error) {
      console.error('加载新品推荐失败:', error)
      this.setData({ loading: false })
    } finally {
      hideLoading()
    }
  },

  // 加载更多菜品
  async loadMoreDishes() {
    const nextPage = this.data.page + 1
    this.setData({ page: nextPage })
    
    if (this.data.currentCategoryId) {
      await this.loadDishesByCategory(this.data.currentCategoryId)
    } else {
      await this.loadAllDishes()
    }
  },

  // 选择分类
  onCategoryTap(e) {
    const { item } = e.currentTarget.dataset
    
    this.setData({
      currentCategoryId: item.id,
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    this.loadDishesByCategory(item.id)
  },

  // 全部分类
  onAllCategoryTap() {
    this.setData({
      currentCategoryId: null,
      page: 1,
      hasMore: true,
      dishes: []
    })
    
    this.loadAllDishes()
  },

  // 搜索输入
  onSearchInput(e) {
    this.setData({
      searchValue: e.detail.value
    })
  },

  // 搜索确认
  onSearchConfirm() {
    const { searchValue } = this.data
    if (searchValue.trim()) {
      wx.navigateTo({
        url: `/pages/search/search?keyword=${encodeURIComponent(searchValue)}`
      })
    }
  },

  // 点击菜品
  onDishTap(e) {
    const { item } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/detail/detail?id=${item.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    e.stopPropagation()
    
    const { item } = e.currentTarget.dataset
    const app = getApp()
    
    app.addToCart({
      id: item.id,
      name: item.name,
      price: item.price,
      image: item.image,
      quantity: 1
    })
    
    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })
    
    this.updateCartCount()
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 获取图片URL
  getImageUrl(path) {
    return getImageUrl(path)
  }
})
