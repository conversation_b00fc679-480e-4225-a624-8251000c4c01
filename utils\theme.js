// utils/theme.js
class ThemeManager {
  constructor() {
    this.currentTheme = 'default'
    this.themes = {
      default: {
        name: '经典橙',
        primary: '#ff6b35',
        primaryGradient: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
        secondary: '#667eea',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        success: '#2ed573',
        warning: '#ffa502',
        danger: '#ff4757',
        textColor: '#2f3542',
        textLight: '#57606f'
      },
      blue: {
        name: '海洋蓝',
        primary: '#3742fa',
        primaryGradient: 'linear-gradient(135deg, #3742fa 0%, #2f3542 100%)',
        secondary: '#2ed573',
        background: 'linear-gradient(135deg, #74b9ff 0%, #0984e3 100%)',
        success: '#00b894',
        warning: '#fdcb6e',
        danger: '#e84393',
        textColor: '#2d3436',
        textLight: '#636e72'
      },
      green: {
        name: '自然绿',
        primary: '#00b894',
        primaryGradient: 'linear-gradient(135deg, #00b894 0%, #00a085 100%)',
        secondary: '#6c5ce7',
        background: 'linear-gradient(135deg, #a8e6cf 0%, #7fcdcd 100%)',
        success: '#00b894',
        warning: '#fdcb6e',
        danger: '#e84393',
        textColor: '#2d3436',
        textLight: '#636e72'
      },
      purple: {
        name: '梦幻紫',
        primary: '#6c5ce7',
        primaryGradient: 'linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%)',
        secondary: '#fd79a8',
        background: 'linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%)',
        success: '#00b894',
        warning: '#fdcb6e',
        danger: '#e84393',
        textColor: '#2d3436',
        textLight: '#636e72'
      },
      dark: {
        name: '暗夜黑',
        primary: '#ff6b35',
        primaryGradient: 'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)',
        secondary: '#74b9ff',
        background: 'linear-gradient(135deg, #2d3436 0%, #636e72 100%)',
        success: '#00b894',
        warning: '#fdcb6e',
        danger: '#e84393',
        textColor: '#ddd',
        textLight: '#b2bec3'
      }
    }
    
    this.loadTheme()
  }

  // 加载主题
  loadTheme() {
    try {
      const savedTheme = wx.getStorageSync('currentTheme')
      if (savedTheme && this.themes[savedTheme]) {
        this.currentTheme = savedTheme
      }
    } catch (error) {
      console.error('加载主题失败:', error)
    }
    
    this.applyTheme()
  }

  // 应用主题
  applyTheme() {
    const theme = this.themes[this.currentTheme]
    if (!theme) return

    // 更新CSS变量
    const pages = getCurrentPages()
    if (pages.length > 0) {
      const currentPage = pages[pages.length - 1]
      if (currentPage.setData) {
        currentPage.setData({
          themeVars: {
            '--primary-color': theme.primary,
            '--primary-gradient': theme.primaryGradient,
            '--secondary-color': theme.secondary,
            '--background-gradient': theme.background,
            '--success-color': theme.success,
            '--warning-color': theme.warning,
            '--danger-color': theme.danger,
            '--text-color': theme.textColor,
            '--text-light': theme.textLight
          }
        })
      }
    }

    // 触发主题变更事件
    this.triggerThemeChange()
  }

  // 切换主题
  switchTheme(themeName) {
    if (!this.themes[themeName]) {
      console.error('主题不存在:', themeName)
      return false
    }

    this.currentTheme = themeName
    
    try {
      wx.setStorageSync('currentTheme', themeName)
    } catch (error) {
      console.error('保存主题失败:', error)
    }
    
    this.applyTheme()
    return true
  }

  // 获取当前主题
  getCurrentTheme() {
    return {
      name: this.currentTheme,
      ...this.themes[this.currentTheme]
    }
  }

  // 获取所有主题
  getAllThemes() {
    return Object.keys(this.themes).map(key => ({
      key,
      ...this.themes[key]
    }))
  }

  // 触发主题变更事件
  triggerThemeChange() {
    // 可以在这里添加全局事件通知
    const app = getApp()
    if (app.globalData) {
      app.globalData.currentTheme = this.currentTheme
    }
  }

  // 获取主题颜色
  getThemeColor(colorName) {
    const theme = this.themes[this.currentTheme]
    return theme[colorName] || theme.primary
  }

  // 检查是否为暗色主题
  isDarkTheme() {
    return this.currentTheme === 'dark'
  }
}

// 创建全局主题管理器实例
const themeManager = new ThemeManager()

module.exports = {
  themeManager,
  ThemeManager
}
