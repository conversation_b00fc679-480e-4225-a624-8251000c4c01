package com.foodapp.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.foodapp.entity.Dish;
import com.foodapp.mapper.DishMapper;
import com.foodapp.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 菜品服务实现类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class DishServiceImpl implements DishService {

    @Autowired
    private DishMapper dishMapper;

    @Override
    public IPage<Dish> page(Integer current, Integer size, Long categoryId, String keyword, String sortBy, Boolean isHot, Boolean isNew) {
        Page<Dish> page = new Page<>(current, size);
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        
        // 状态过滤：只查询上架的菜品
        queryWrapper.eq("status", 1);
        
        // 分类过滤
        if (categoryId != null) {
            queryWrapper.eq("category_id", categoryId);
        }
        
        // 关键词搜索
        if (keyword != null && !keyword.trim().isEmpty()) {
            queryWrapper.and(wrapper -> wrapper
                .like("name", keyword)
                .or()
                .like("description", keyword)
                .or()
                .like("tags", keyword)
            );
        }
        
        // 热门过滤
        if (isHot != null && isHot) {
            queryWrapper.eq("is_hot", true);
        }
        
        // 新品过滤
        if (isNew != null && isNew) {
            queryWrapper.eq("is_new", true);
        }
        
        // 排序
        if ("price_asc".equals(sortBy)) {
            queryWrapper.orderByAsc("price");
        } else if ("price_desc".equals(sortBy)) {
            queryWrapper.orderByDesc("price");
        } else if ("rating".equals(sortBy)) {
            queryWrapper.orderByDesc("rating");
        } else if ("sales".equals(sortBy)) {
            queryWrapper.orderByDesc("sales");
        } else {
            // 默认排序：热门 > 新品 > 推荐 > 评分 > 销量
            queryWrapper.orderByDesc("is_hot")
                       .orderByDesc("is_new")
                       .orderByDesc("is_recommend")
                       .orderByDesc("rating")
                       .orderByDesc("sales");
        }
        
        return dishMapper.selectPage(page, queryWrapper);
    }

    @Override
    public Dish getDetailById(Long id) {
        return dishMapper.selectById(id);
    }

    @Override
    public List<Dish> getHotDishes(Integer limit) {
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .eq("is_hot", true)
                   .orderByDesc("sales")
                   .orderByDesc("rating")
                   .last("LIMIT " + limit);
        
        return dishMapper.selectList(queryWrapper);
    }

    @Override
    public List<Dish> getNewDishes(Integer limit) {
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .eq("is_new", true)
                   .orderByDesc("create_time")
                   .orderByDesc("rating")
                   .last("LIMIT " + limit);
        
        return dishMapper.selectList(queryWrapper);
    }

    @Override
    public List<Dish> getRecommendDishes(Integer limit) {
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .eq("is_recommend", true)
                   .orderByDesc("rating")
                   .orderByDesc("sales")
                   .last("LIMIT " + limit);
        
        return dishMapper.selectList(queryWrapper);
    }

    @Override
    public List<Dish> searchDishes(String keyword, Integer limit) {
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .and(wrapper -> wrapper
                       .like("name", keyword)
                       .or()
                       .like("description", keyword)
                       .or()
                       .like("tags", keyword)
                   )
                   .orderByDesc("rating")
                   .orderByDesc("sales")
                   .last("LIMIT " + limit);
        
        return dishMapper.selectList(queryWrapper);
    }

    @Override
    public List<Dish> getDishesByCategory(Long categoryId, Integer limit) {
        QueryWrapper<Dish> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1)
                   .eq("category_id", categoryId)
                   .orderByDesc("is_hot")
                   .orderByDesc("is_new")
                   .orderByDesc("rating")
                   .orderByDesc("sales")
                   .last("LIMIT " + limit);
        
        return dishMapper.selectList(queryWrapper);
    }

    @Override
    public Map<String, Object> getStats() {
        Map<String, Object> stats = new HashMap<>();
        
        // 总菜品数
        QueryWrapper<Dish> totalWrapper = new QueryWrapper<>();
        totalWrapper.eq("status", 1);
        Long totalCount = dishMapper.selectCount(totalWrapper);
        stats.put("totalCount", totalCount);
        
        // 热门菜品数
        QueryWrapper<Dish> hotWrapper = new QueryWrapper<>();
        hotWrapper.eq("status", 1).eq("is_hot", true);
        Long hotCount = dishMapper.selectCount(hotWrapper);
        stats.put("hotCount", hotCount);
        
        // 新品数
        QueryWrapper<Dish> newWrapper = new QueryWrapper<>();
        newWrapper.eq("status", 1).eq("is_new", true);
        Long newCount = dishMapper.selectCount(newWrapper);
        stats.put("newCount", newCount);
        
        // 推荐菜品数
        QueryWrapper<Dish> recommendWrapper = new QueryWrapper<>();
        recommendWrapper.eq("status", 1).eq("is_recommend", true);
        Long recommendCount = dishMapper.selectCount(recommendWrapper);
        stats.put("recommendCount", recommendCount);
        
        return stats;
    }

    @Override
    public void increaseSales(Long id, Integer quantity) {
        Dish dish = dishMapper.selectById(id);
        if (dish != null) {
            dish.setSales(dish.getSales() + quantity);
            dishMapper.updateById(dish);
        }
    }

    @Override
    public void updateRating(Long id, Double rating) {
        Dish dish = dishMapper.selectById(id);
        if (dish != null) {
            // 这里可以实现更复杂的评分计算逻辑
            // 比如基于历史评分和新评分计算平均值
            dish.setRating(java.math.BigDecimal.valueOf(rating));
            dishMapper.updateById(dish);
        }
    }
}
