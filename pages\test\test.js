// pages/test/test.js
const app = getApp()

Page({
  data: {
    testResults: [],
    isLoading: false,
    baseUrl: ''
  },

  onLoad() {
    this.setData({
      baseUrl: app.globalData.baseUrl
    })
    this.runAllTests()
  },

  async runAllTests() {
    this.setData({ isLoading: true, testResults: [] })

    const tests = [
      { name: '健康检查', url: '/health' },
      { name: '热门菜品', url: '/dish/hot' },
      { name: '新品推荐', url: '/dish/new' },
      { name: '分类列表', url: '/categories' },
      { name: '轮播图', url: '/banners' }
    ]

    for (const test of tests) {
      await this.runSingleTest(test)
    }

    this.setData({ isLoading: false })
  },

  async runSingleTest(test) {
    const startTime = Date.now()

    try {
      const result = await this.makeRequest(test.url)
      const endTime = Date.now()

      this.addTestResult({
        name: test.name,
        status: 'success',
        message: `请求成功 (${endTime - startTime}ms)`,
        data: result
      })
    } catch (error) {
      this.addTestResult({
        name: test.name,
        status: 'error',
        message: error.message || '请求失败',
        data: null
      })
    }
  },

  makeRequest(url) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: app.globalData.baseUrl + url,
        method: 'GET',
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        },
        fail: (err) => {
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      })
    })
  },

  addTestResult(result) {
    const results = this.data.testResults
    results.push(result)
    this.setData({ testResults: results })
  },

  onRetryTest() {
    this.runAllTests()
  },

  onViewDetails(e) {
    const index = e.currentTarget.dataset.index
    const result = this.data.testResults[index]

    wx.showModal({
      title: result.name,
      content: JSON.stringify(result.data, null, 2),
      showCancel: false
    })
  }
})
