<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后端服务状态</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .status-card:hover {
            transform: translateY(-5px);
        }
        
        .status-header {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .status-icon {
            font-size: 24px;
            margin-right: 10px;
        }
        
        .status-title {
            font-size: 18px;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .status-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .status-description {
            color: #7f8c8d;
            font-size: 14px;
        }
        
        .status-online {
            color: #2ecc71;
        }
        
        .status-offline {
            color: #e74c3c;
        }
        
        .api-list {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }
        
        .api-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            border-bottom: 1px solid #eee;
            transition: background 0.3s ease;
        }
        
        .api-item:hover {
            background: #f8f9fa;
        }
        
        .api-item:last-child {
            border-bottom: none;
        }
        
        .api-method {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            color: white;
            margin-right: 10px;
        }
        
        .method-get { background: #2ecc71; }
        .method-post { background: #3498db; }
        .method-put { background: #f39c12; }
        .method-delete { background: #e74c3c; }
        
        .api-url {
            font-family: 'Courier New', monospace;
            color: #2c3e50;
        }
        
        .api-status {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 10px;
        }
        
        .status-success { background: #2ecc71; }
        .status-error { background: #e74c3c; }
        .status-loading { background: #f39c12; }
        
        .refresh-btn {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 20px auto;
            display: block;
            transition: transform 0.2s ease;
        }
        
        .refresh-btn:hover {
            transform: scale(1.05);
        }
        
        .last-update {
            text-align: center;
            color: #7f8c8d;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🍽️ 后端服务状态监控</h1>
        
        <div class="status-grid">
            <div class="status-card">
                <div class="status-header">
                    <span class="status-icon">🚀</span>
                    <span class="status-title">服务状态</span>
                </div>
                <div class="status-value" id="serviceStatus">检查中...</div>
                <div class="status-description">后端服务运行状态</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <span class="status-icon">⏱️</span>
                    <span class="status-title">响应时间</span>
                </div>
                <div class="status-value" id="responseTime">-</div>
                <div class="status-description">API响应延迟</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <span class="status-icon">📊</span>
                    <span class="status-title">数据统计</span>
                </div>
                <div class="status-value" id="dataStats">-</div>
                <div class="status-description">菜品和分类数量</div>
            </div>
            
            <div class="status-card">
                <div class="status-header">
                    <span class="status-icon">🔗</span>
                    <span class="status-title">API端点</span>
                </div>
                <div class="status-value" id="apiCount">-</div>
                <div class="status-description">可用接口数量</div>
            </div>
        </div>
        
        <div class="api-list">
            <h2 style="margin-bottom: 20px; color: #2c3e50;">📋 API接口状态</h2>
            <div id="apiList">
                <div class="api-item">
                    <div>
                        <span class="api-method method-get">GET</span>
                        <span class="api-url">/ - 首页</span>
                    </div>
                    <div class="api-status status-loading"></div>
                </div>
            </div>
        </div>
        
        <button class="refresh-btn" onclick="checkAllStatus()">🔄 刷新状态</button>
        
        <div class="last-update" id="lastUpdate">
            最后更新: -
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080';
        
        const endpoints = [
            { method: 'GET', path: '/', name: '首页' },
            { method: 'GET', path: '/api/health', name: '健康检查' },
            { method: 'GET', path: '/api/dish/page', name: '菜品列表' },
            { method: 'GET', path: '/api/dish/hot', name: '热门菜品' },
            { method: 'GET', path: '/api/categories', name: '分类列表' },
            { method: 'GET', path: '/api/banners', name: '轮播图' },
            { method: 'POST', path: '/api/file/upload', name: '文件上传' }
        ];

        async function checkEndpoint(endpoint) {
            const startTime = Date.now();
            try {
                const response = await fetch(API_BASE + endpoint.path, {
                    method: endpoint.method === 'POST' ? 'GET' : endpoint.method // POST接口用GET测试连通性
                });
                const endTime = Date.now();
                return {
                    success: response.ok,
                    status: response.status,
                    responseTime: endTime - startTime
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    responseTime: Date.now() - startTime
                };
            }
        }

        async function checkAllStatus() {
            const serviceStatusEl = document.getElementById('serviceStatus');
            const responseTimeEl = document.getElementById('responseTime');
            const dataStatsEl = document.getElementById('dataStats');
            const apiCountEl = document.getElementById('apiCount');
            const apiListEl = document.getElementById('apiList');
            const lastUpdateEl = document.getElementById('lastUpdate');

            // 重置状态
            serviceStatusEl.textContent = '检查中...';
            serviceStatusEl.className = '';
            
            // 清空API列表
            apiListEl.innerHTML = '';
            
            let totalResponseTime = 0;
            let successCount = 0;
            
            // 检查所有端点
            for (const endpoint of endpoints) {
                const apiItem = document.createElement('div');
                apiItem.className = 'api-item';
                
                const methodClass = `method-${endpoint.method.toLowerCase()}`;
                apiItem.innerHTML = `
                    <div>
                        <span class="api-method ${methodClass}">${endpoint.method}</span>
                        <span class="api-url">${endpoint.path} - ${endpoint.name}</span>
                    </div>
                    <div class="api-status status-loading"></div>
                `;
                
                apiListEl.appendChild(apiItem);
                
                // 检查端点状态
                const result = await checkEndpoint(endpoint);
                const statusEl = apiItem.querySelector('.api-status');
                
                if (result.success) {
                    statusEl.className = 'api-status status-success';
                    successCount++;
                    totalResponseTime += result.responseTime;
                } else {
                    statusEl.className = 'api-status status-error';
                }
            }
            
            // 更新总体状态
            if (successCount > 0) {
                serviceStatusEl.textContent = '🟢 在线';
                serviceStatusEl.className = 'status-online';
                
                const avgResponseTime = Math.round(totalResponseTime / successCount);
                responseTimeEl.textContent = `${avgResponseTime}ms`;
                
                // 获取数据统计
                try {
                    const dishResponse = await fetch(API_BASE + '/api/dish/hot');
                    const categoryResponse = await fetch(API_BASE + '/api/categories');
                    
                    if (dishResponse.ok && categoryResponse.ok) {
                        const dishData = await dishResponse.json();
                        const categoryData = await categoryResponse.json();
                        dataStatsEl.textContent = `${dishData.data.length} 菜品, ${categoryData.data.length} 分类`;
                    }
                } catch (error) {
                    dataStatsEl.textContent = '获取失败';
                }
                
            } else {
                serviceStatusEl.textContent = '🔴 离线';
                serviceStatusEl.className = 'status-offline';
                responseTimeEl.textContent = '超时';
                dataStatsEl.textContent = '无法连接';
            }
            
            apiCountEl.textContent = `${successCount}/${endpoints.length}`;
            lastUpdateEl.textContent = `最后更新: ${new Date().toLocaleString()}`;
        }

        // 页面加载时检查状态
        window.onload = checkAllStatus;
        
        // 每30秒自动刷新
        setInterval(checkAllStatus, 30000);
    </script>
</body>
</html>
