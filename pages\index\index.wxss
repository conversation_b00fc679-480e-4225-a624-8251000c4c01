/* pages/index/index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 200rpx;
}

/* 轮播图 */
.banner-section {
  margin: 20rpx;
  border-radius: 24rpx;
  overflow: hidden;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.2);
}

.banner-swiper {
  height: 360rpx;
  border-radius: 24rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.banner-image {
  width: 100%;
  height: 100%;
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 200rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.6));
  display: flex;
  align-items: flex-end;
  padding: 40rpx;
}

.banner-content {
  color: white;
}

.banner-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 24rpx;
  opacity: 0.9;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

/* 分类导航 */
.category-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2f3542;
  margin-bottom: 30rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 20rpx;
}

.category-item {
  width: calc(25% - 15rpx);
  text-align: center;
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 20rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 107, 53, 0.1);
}

.category-item:active {
  transform: scale(0.95);
  background: rgba(255, 107, 53, 0.1);
}

.category-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-bottom: 15rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.category-name {
  font-size: 24rpx;
  color: #2f3542;
  font-weight: 500;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.section-title-wrapper {
  display: flex;
  align-items: center;
  flex-direction: column;
  align-items: flex-start;
}

.section-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  margin-bottom: 4rpx;
}

.section-subtitle {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.8);
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.3);
}

.section-more {
  font-size: 28rpx;
  color: white;
  background: rgba(255, 255, 255, 0.2);
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.section-more:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 热门菜品 */
.hot-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 40rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.dish-scroll {
  white-space: nowrap;
}

.dish-list {
  display: flex;
  padding: 0 20rpx;
  gap: 20rpx;
}

.dish-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.dish-item:last-child {
  margin-right: 30rpx;
}

.dish-image {
  width: 100%;
  height: 200rpx;
}

.dish-info {
  padding: 20rpx;
}

.dish-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.dish-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 新品推荐 */
.new-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 24rpx;
  padding: 40rpx 0;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.new-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 20rpx;
}

.new-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.new-item:last-child {
  border-bottom: none;
}

.new-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.new-info {
  flex: 1;
}

.new-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.new-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.new-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.new-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 加载和空状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 200rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}
