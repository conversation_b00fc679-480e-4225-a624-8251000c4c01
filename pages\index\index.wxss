/* pages/index/index.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding-bottom: 200rpx;
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(46, 213, 115, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(116, 75, 162, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.container > * {
  position: relative;
  z-index: 1;
}

/* 轮播图 */
.banner-section {
  margin: 20rpx;
  border-radius: 32rpx;
  overflow: hidden;
  box-shadow: 0 16rpx 48rpx rgba(0, 0, 0, 0.25);
  position: relative;
  animation: fadeInDown 0.8s ease-out;
}

.banner-section::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg, #ff6b35, #f7931e, #667eea, #764ba2);
  border-radius: 34rpx;
  z-index: -1;
  opacity: 0.6;
  filter: blur(8rpx);
}

.banner-swiper {
  height: 380rpx;
  border-radius: 32rpx;
  overflow: hidden;
}

.banner-item {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.banner-image {
  width: 100%;
  height: 100%;
  transition: transform 0.6s ease;
}

.banner-item:active .banner-image {
  transform: scale(1.05);
}

.banner-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 220rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  display: flex;
  align-items: flex-end;
  padding: 40rpx;
}

.banner-content {
  color: white;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.banner-title {
  font-size: 38rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 12rpx;
  text-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.4);
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.banner-subtitle {
  font-size: 26rpx;
  opacity: 0.95;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.4);
  font-weight: 500;
}

/* 分类导航 */
.category-section {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 32rpx;
  padding: 50rpx 40rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  position: relative;
  animation: fadeInUp 0.8s ease-out 0.2s both;
}

.category-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 32rpx 32rpx 0 0;
}

.section-title {
  font-size: 38rpx;
  font-weight: bold;
  color: #2f3542;
  margin-bottom: 40rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  text-align: center;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -15rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 2rpx;
}

.category-grid {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 24rpx;
}

.category-item {
  width: calc(25% - 18rpx);
  text-align: center;
  padding: 24rpx 16rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 248, 240, 0.9) 100%);
  border-radius: 24rpx;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  border: 2rpx solid rgba(255, 107, 53, 0.1);
  position: relative;
  overflow: hidden;
}

.category-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 107, 53, 0.1) 0%, rgba(247, 147, 30, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-item:active {
  transform: scale(0.95) translateY(2rpx);
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.2);
}

.category-item:active::before {
  opacity: 1;
}

.category-icon {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
  box-shadow: 0 6rpx 16rpx rgba(0, 0, 0, 0.15);
  transition: transform 0.3s ease;
  position: relative;
  z-index: 1;
}

.category-item:active .category-icon {
  transform: scale(1.1);
}

.category-name {
  font-size: 26rpx;
  color: #2f3542;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

/* 区块头部 */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 40rpx;
  position: relative;
}

.section-title-wrapper {
  display: flex;
  align-items: flex-start;
  flex-direction: row;
  gap: 16rpx;
}

.section-icon {
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.3));
  animation: bounce 2s ease-in-out infinite;
}

.section-title {
  font-size: 40rpx;
  font-weight: bold;
  color: white;
  text-shadow: 0 3rpx 6rpx rgba(0, 0, 0, 0.4);
  margin-bottom: 6rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.section-subtitle {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 500;
}

.section-more {
  font-size: 28rpx;
  color: white;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  padding: 16rpx 32rpx;
  border-radius: 40rpx;
  backdrop-filter: blur(15rpx);
  border: 2rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  position: relative;
  overflow: hidden;
}

.section-more::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.section-more:active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.2) 100%);
  transform: scale(0.95) translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
}

.section-more:active::before {
  left: 100%;
}

/* 热门菜品 */
.hot-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%);
  backdrop-filter: blur(25rpx);
  margin: 20rpx;
  border-radius: 32rpx;
  padding: 50rpx 0;
  border: 2rpx solid rgba(255, 255, 255, 0.25);
  position: relative;
  animation: fadeInUp 0.8s ease-out 0.4s both;
  overflow: hidden;
}

.hot-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 10% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 90% 80%, rgba(46, 213, 115, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.hot-section > * {
  position: relative;
  z-index: 1;
}

.dish-scroll {
  white-space: nowrap;
  overflow-x: auto;
  overflow-y: hidden;
  -webkit-overflow-scrolling: touch;
}

.dish-scroll::-webkit-scrollbar {
  display: none;
}

.dish-list {
  display: flex;
  padding: 0 20rpx;
  gap: 24rpx;
  animation: fadeInLeft 0.8s ease-out 0.6s both;
}

.dish-item {
  display: inline-block;
  width: 280rpx;
  margin-right: 20rpx;
  background-color: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
}

.dish-item:last-child {
  margin-right: 30rpx;
}

.dish-image {
  width: 100%;
  height: 200rpx;
}

.dish-info {
  padding: 20rpx;
}

.dish-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.dish-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dish-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.add-btn {
  width: 60rpx;
  height: 60rpx;
  background-color: #ff6b35;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-icon {
  color: white;
  font-size: 32rpx;
  font-weight: bold;
}

/* 新品推荐 */
.new-section {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%);
  backdrop-filter: blur(25rpx);
  margin: 20rpx;
  border-radius: 32rpx;
  padding: 50rpx 0;
  border: 2rpx solid rgba(255, 255, 255, 0.25);
  position: relative;
  animation: fadeInUp 0.8s ease-out 0.6s both;
  overflow: hidden;
}

.new-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 80% 20%, rgba(46, 213, 115, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.new-section > * {
  position: relative;
  z-index: 1;
}

.new-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
  padding: 0 20rpx;
}

.new-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.new-item:last-child {
  border-bottom: none;
}

.new-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.new-info {
  flex: 1;
}

.new-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 10rpx;
}

.new-desc {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 15rpx;
  line-height: 1.4;
}

.new-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.new-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 加载和空状态 */
.loading {
  text-align: center;
  padding: 100rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}

.empty-state {
  text-align: center;
  padding: 200rpx 40rpx;
  color: #999;
  font-size: 28rpx;
}
