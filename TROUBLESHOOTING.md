# 🔧 小程序网络连接故障排除指南

## 🚨 当前状态
- ✅ **后端服务**: 正常运行 (localhost:8080)
- ✅ **API接口**: 全部可用
- ❌ **小程序连接**: 需要配置

## 📋 故障排除步骤

### 步骤1: 检查微信开发者工具设置 ⚙️

1. **打开项目详情**
   - 在微信开发者工具中，点击右上角的"详情"按钮

2. **本地设置配置**
   ```
   ✅ 不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书
   ✅ 开启调试模式
   ✅ 启用网络调试
   ```

3. **清除缓存**
   - 点击"清缓存" → "清除全部缓存"
   - 重新编译项目

### 步骤2: 验证后端服务 🖥️

1. **浏览器测试**
   - 打开浏览器访问: http://localhost:8080
   - 应该看到JSON响应数据

2. **API接口测试**
   ```
   http://localhost:8080/api/health
   http://localhost:8080/api/dish/hot
   http://localhost:8080/api/categories
   ```

### 步骤3: 小程序网络测试 📱

1. **使用内置测试功能**
   - 在小程序首页，如果一直显示"加载中..."
   - 点击"🔗 测试网络连接"按钮
   - 查看测试结果

2. **查看控制台日志**
   - 打开微信开发者工具的"Console"面板
   - 查看网络请求日志和错误信息

### 步骤4: 常见问题解决 🛠️

#### 问题1: 域名校验错误
**症状**: 控制台显示"不在以下 request 合法域名列表中"
**解决**: 
- 确保已关闭"校验合法域名"选项
- 重启微信开发者工具

#### 问题2: 连接超时
**症状**: 请求一直pending或超时
**解决**: 
1. 检查防火墙设置
2. 确认后端服务正在运行
3. 尝试重启微信开发者工具

#### 问题3: 端口被占用
**症状**: 后端无法启动
**解决**: 
```bash
# 检查端口占用
netstat -ano | findstr :8080
# 杀死占用进程或修改端口
```

#### 问题4: 网络权限
**症状**: 小程序无法发起网络请求
**解决**: 
- 检查project.config.json中的网络配置
- 确保"urlCheck": false

### 步骤5: 高级调试 🔍

#### 使用网络面板
1. 打开微信开发者工具的"Network"面板
2. 重新加载小程序
3. 查看网络请求状态

#### 手动测试API
在小程序控制台中运行：
```javascript
wx.request({
  url: 'http://localhost:8080/api/health',
  method: 'GET',
  success: (res) => {
    console.log('连接成功:', res.data);
  },
  fail: (err) => {
    console.error('连接失败:', err);
  }
});
```

### 步骤6: 备用方案 🔄

#### 方案1: 使用本机IP
```javascript
// 在app.js中修改baseUrl
globalData: {
  baseUrl: 'http://*************:8080/api' // 替换为你的实际IP
}
```

#### 方案2: 使用代理工具
```bash
# 安装ngrok
npm install -g ngrok
# 创建公网隧道
ngrok http 8080
```

#### 方案3: 检查网络环境
- 确保电脑和手机在同一网络
- 关闭VPN或代理软件
- 检查路由器设置

### 步骤7: 验证修复 ✅

完成配置后，验证以下功能：

1. **基础连接**
   - 小程序能够启动
   - 不再显示"加载中..."

2. **数据加载**
   - 轮播图正常显示
   - 分类列表加载成功
   - 菜品数据显示正常

3. **交互功能**
   - 点击分类可以跳转
   - 菜品详情可以查看
   - 购物车功能正常

## 🆘 如果问题仍然存在

### 收集信息
1. 微信开发者工具版本
2. 控制台错误信息截图
3. 网络面板请求状态
4. 后端服务日志

### 检查清单
- [ ] 后端服务正在运行
- [ ] 浏览器可以访问API
- [ ] 微信开发者工具设置正确
- [ ] 项目配置文件正确
- [ ] 网络环境正常

### 重启大法
1. 重启后端服务
2. 重启微信开发者工具
3. 重新编译小程序项目
4. 清除所有缓存

---

💡 **提示**: 大多数连接问题都是由于微信开发者工具的域名校验设置导致的。确保正确配置后，小程序应该能够正常连接后端服务。
