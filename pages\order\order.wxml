<!--pages/order/order.wxml-->
<view class="container">
  <!-- 配送信息 -->
  <view class="delivery-section">
    <view class="section-title">
      <text class="title-icon">📍</text>
      <text class="title-text">配送信息</text>
    </view>
    
    <view class="form-item">
      <text class="form-label">联系人</text>
      <input 
        class="form-input"
        placeholder="请输入联系人姓名"
        value="{{contactName}}"
        bindinput="onContactNameInput"
      />
    </view>
    
    <view class="form-item">
      <text class="form-label">手机号</text>
      <input 
        class="form-input"
        type="number"
        placeholder="请输入联系人手机号"
        value="{{contactPhone}}"
        bindinput="onContactPhoneInput"
      />
    </view>
    
    <view class="form-item address-item">
      <text class="form-label">配送地址</text>
      <view class="address-input">
        <textarea 
          class="form-textarea"
          placeholder="请输入详细配送地址"
          value="{{deliveryAddress}}"
          bindinput="onDeliveryAddressInput"
          maxlength="200"
        />
        <button class="choose-address-btn" bindtap="onChooseAddress">
          选择地址
        </button>
      </view>
    </view>
  </view>

  <!-- 订单商品 -->
  <view class="order-items-section">
    <view class="section-title">
      <text class="title-icon">🛒</text>
      <text class="title-text">订单商品</text>
      <text class="item-count">({{totalCount}}件)</text>
    </view>
    
    <view class="order-items">
      <view 
        class="order-item" 
        wx:for="{{orderItems}}" 
        wx:key="id"
      >
        <image 
          class="item-image" 
          src="{{item.image || '/images/dish-default.jpg'}}" 
          mode="aspectFill"
        />
        <view class="item-info">
          <text class="item-name">{{item.name}}</text>
          <view class="item-details">
            <text class="item-price">¥{{item.price}}</text>
            <text class="item-quantity">×{{item.quantity}}</text>
          </view>
        </view>
        <text class="item-subtotal">¥{{item.subtotal.toFixed(2)}}</text>
      </view>
    </view>
  </view>

  <!-- 备注信息 -->
  <view class="remark-section">
    <view class="section-title">
      <text class="title-icon">📝</text>
      <text class="title-text">备注信息</text>
    </view>
    
    <textarea 
      class="remark-input"
      placeholder="请输入备注信息（选填）"
      value="{{remark}}"
      bindinput="onRemarkInput"
      maxlength="100"
    />
  </view>

  <!-- 订单总计 -->
  <view class="total-section">
    <view class="total-row">
      <text class="total-label">商品总计</text>
      <text class="total-value">¥{{totalPrice}}</text>
    </view>
    <view class="total-row">
      <text class="total-label">配送费</text>
      <text class="total-value free">免费</text>
    </view>
    <view class="total-row final">
      <text class="total-label">实付金额</text>
      <text class="total-value final-price">¥{{totalPrice}}</text>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="total-info">
      <text class="total-text">合计：</text>
      <text class="total-amount">¥{{totalPrice}}</text>
    </view>
    <view class="action-buttons">
      <button class="back-btn" bindtap="onBackToCart">
        返回购物车
      </button>
      <button 
        class="submit-btn {{submitting ? 'submitting' : ''}}" 
        bindtap="onSubmitOrder"
        disabled="{{submitting}}"
      >
        {{submitting ? '提交中...' : '提交订单'}}
      </button>
    </view>
  </view>
</view>
