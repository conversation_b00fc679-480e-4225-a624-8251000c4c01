/* components/dish-card/dish-card.wxss */
.dish-card {
  position: relative;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.dish-card:active {
  transform: scale(0.98);
}

/* 普通模式 */
.dish-card-normal {
  margin: 16rpx;
}

/* 紧凑模式 */
.dish-card-compact {
  margin: 8rpx;
  border-radius: 16rpx;
}

.dish-card-compact .content {
  padding: 20rpx;
}

/* 特色模式 */
.dish-card-featured {
  margin: 20rpx;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  box-shadow: 0 12rpx 40rpx rgba(255, 107, 53, 0.15);
}

.image-container {
  position: relative;
  height: 300rpx;
  overflow: hidden;
}

.dish-card-compact .image-container {
  height: 200rpx;
}

.dish-card-featured .image-container {
  height: 350rpx;
}

.dish-image {
  width: 100%;
  height: 100%;
  transition: transform 0.6s ease;
}

.dish-card:active .dish-image {
  transform: scale(1.1);
}

.image-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.3));
  pointer-events: none;
}

.tags {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  z-index: 2;
}

.tag {
  padding: 6rpx 16rpx;
  border-radius: 20rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: white;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.tag.hot {
  background: linear-gradient(135deg, #ff4757 0%, #ff3742 100%);
  box-shadow: 0 4rpx 12rpx rgba(255, 71, 87, 0.4);
}

.tag.new {
  background: linear-gradient(135deg, #2ed573 0%, #17c0eb 100%);
  box-shadow: 0 4rpx 12rpx rgba(46, 213, 115, 0.4);
}

.like-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10rpx);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  transition: all 0.3s ease;
  z-index: 2;
}

.like-btn.liked {
  background: rgba(255, 71, 87, 0.9);
  border-color: #ff4757;
  transform: scale(1.1);
}

.like-icon {
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.content {
  padding: 24rpx;
}

.dish-info {
  margin-bottom: 20rpx;
}

.dish-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #2f3542;
  display: block;
  margin-bottom: 8rpx;
  line-height: 1.3;
}

.dish-card-featured .dish-name {
  font-size: 36rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.dish-desc {
  font-size: 24rpx;
  color: #57606f;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  margin-bottom: 12rpx;
}

.meta-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.sales, .rating {
  display: flex;
  align-items: center;
  gap: 6rpx;
}

.sales-icon, .rating-stars {
  font-size: 20rpx;
}

.sales-text, .rating-text {
  font-size: 22rpx;
  color: #57606f;
}

.footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.price-section {
  display: flex;
  align-items: baseline;
  gap: 4rpx;
}

.price {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

.dish-card-featured .price {
  font-size: 42rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.price-unit {
  font-size: 24rpx;
  color: #57606f;
}

.add-btn {
  width: 64rpx;
  height: 64rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 20rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.add-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3) 0%, transparent 100%);
  border-radius: 50%;
}

.add-btn:active {
  transform: scale(0.9);
  box-shadow: 0 3rpx 10rpx rgba(255, 107, 53, 0.4);
}

.add-icon {
  font-size: 32rpx;
  color: white;
  font-weight: bold;
  position: relative;
  z-index: 1;
}

/* 特色装饰 */
.featured-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  overflow: hidden;
}

.sparkle {
  position: absolute;
  font-size: 24rpx;
  animation: sparkle 3s infinite;
}

.sparkle-1 {
  top: 20%;
  left: 10%;
  animation-delay: 0s;
}

.sparkle-2 {
  top: 60%;
  right: 15%;
  animation-delay: 1s;
}

.sparkle-3 {
  bottom: 25%;
  left: 20%;
  animation-delay: 2s;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0.5) rotate(0deg);
  }
  50% {
    opacity: 1;
    transform: scale(1) rotate(180deg);
  }
}

/* 悬浮光效 */
.hover-glow {
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 28rpx;
  opacity: 0;
  filter: blur(20rpx);
  z-index: -1;
  transition: opacity 0.3s ease;
}

.dish-card:hover .hover-glow {
  opacity: 0.3;
}
