/* pages/cart/cart.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* 空购物车 */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
  margin-bottom: 40rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 60rpx;
}

.continue-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
}

/* 购物车内容 */
.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 头部操作栏 */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.select-all {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.checkbox.checked {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  font-size: 28rpx;
  color: #ff6b35;
}

/* 商品列表 */
.cart-list {
  flex: 1;
  background-color: white;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

/* 选择框 */
.item-select {
  margin-right: 20rpx;
}

/* 商品信息 */
.item-info {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.item-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.quantity-btn.decrease {
  border-radius: 12rpx 0 0 12rpx;
  color: #666;
}

.quantity-btn.increase {
  border-radius: 0 12rpx 12rpx 0;
  background-color: #ff6b35;
  border-color: #ff6b35;
  color: white;
}

.quantity-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
}

.quantity-text {
  width: 80rpx;
  height: 60rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  background-color: #f8f9fa;
}

/* 删除按钮 */
.item-delete {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  font-size: 32rpx;
  color: #999;
}

/* 底部结算栏 */
.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.footer-info {
  flex: 1;
}

.total-count {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.total-price {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 28rpx;
  color: #333;
}

.price-value {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-left: 10rpx;
}

.checkout-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
  margin-left: 30rpx;
}
