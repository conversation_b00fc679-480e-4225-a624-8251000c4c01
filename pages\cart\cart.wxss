/* pages/cart/cart.wxss */
.container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 107, 53, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(46, 213, 115, 0.1) 0%, transparent 50%);
  pointer-events: none;
  z-index: 0;
}

.container > * {
  position: relative;
  z-index: 1;
}

/* 空购物车 */
.empty-cart {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  margin: 20rpx;
  border-radius: 32rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  animation: fadeInUp 0.8s ease-out;
}

.empty-image {
  width: 320rpx;
  height: 320rpx;
  margin-bottom: 50rpx;
  opacity: 0.8;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-20rpx);
  }
}

.empty-text {
  font-size: 36rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 80rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
  font-weight: 500;
  text-align: center;
}

.continue-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border-radius: 60rpx;
  padding: 24rpx 80rpx;
  font-size: 32rpx;
  border: none;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.continue-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.6s ease;
}

.continue-btn:active {
  transform: scale(0.95);
  box-shadow: 0 4rpx 16rpx rgba(255, 107, 53, 0.4);
}

.continue-btn:active::before {
  left: 100%;
}

/* 购物车内容 */
.cart-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* 头部操作栏 */
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: white;
  border-bottom: 1rpx solid #eee;
}

.select-all {
  display: flex;
  align-items: center;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.checkbox.checked {
  background-color: #ff6b35;
  border-color: #ff6b35;
}

.check-icon {
  color: white;
  font-size: 24rpx;
  font-weight: bold;
}

.select-text {
  font-size: 28rpx;
  color: #333;
}

.clear-btn {
  font-size: 28rpx;
  color: #ff6b35;
}

/* 商品列表 */
.cart-list {
  flex: 1;
  background-color: white;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #eee;
}

.cart-item:last-child {
  border-bottom: none;
}

/* 选择框 */
.item-select {
  margin-right: 20rpx;
}

/* 商品信息 */
.item-info {
  flex: 1;
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.item-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 12rpx;
  margin-right: 20rpx;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.item-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 数量控制 */
.quantity-control {
  display: flex;
  align-items: center;
  margin-right: 20rpx;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.quantity-btn.decrease {
  border-radius: 12rpx 0 0 12rpx;
  color: #666;
}

.quantity-btn.increase {
  border-radius: 0 12rpx 12rpx 0;
  background-color: #ff6b35;
  border-color: #ff6b35;
  color: white;
}

.quantity-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
}

.quantity-text {
  width: 80rpx;
  height: 60rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  background-color: #f8f9fa;
}

/* 删除按钮 */
.item-delete {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.delete-icon {
  font-size: 32rpx;
  color: #999;
}

/* 底部结算栏 */
.cart-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background-color: white;
  border-top: 1rpx solid #eee;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.footer-info {
  flex: 1;
}

.total-count {
  font-size: 24rpx;
  color: #666;
  display: block;
  margin-bottom: 10rpx;
}

.total-price {
  display: flex;
  align-items: center;
}

.price-label {
  font-size: 28rpx;
  color: #333;
}

.price-value {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-left: 10rpx;
}

.checkout-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 50rpx;
  padding: 20rpx 60rpx;
  font-size: 28rpx;
  border: none;
  margin-left: 30rpx;
}
