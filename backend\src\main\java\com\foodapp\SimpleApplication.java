package com.foodapp;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

/**
 * 简化版启动类
 */
@SpringBootApplication
@RestController
public class SimpleApplication {

    public static void main(String[] args) {
        SpringApplication.run(SimpleApplication.class, args);
        System.out.println("=================================");
        System.out.println("🍽️  美食小程序后端启动成功！");
        System.out.println("📱  访问地址: http://localhost:8080");
        System.out.println("🖼️  图片上传: http://localhost:8080/upload");
        System.out.println("=================================");
    }

    @GetMapping("/")
    public Map<String, Object> home() {
        Map<String, Object> result = new HashMap<>();
        result.put("message", "🍽️ 美食小程序后端服务");
        result.put("status", "running");
        result.put("version", "1.0.0");
        result.put("endpoints", new String[]{
            "GET / - 首页",
            "GET /health - 健康检查",
            "POST /upload - 文件上传"
        });
        return result;
    }

    @GetMapping("/health")
    public Map<String, Object> health() {
        Map<String, Object> result = new HashMap<>();
        result.put("status", "UP");
        result.put("timestamp", System.currentTimeMillis());
        return result;
    }
}
