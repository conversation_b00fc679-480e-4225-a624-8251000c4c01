<!--pages/test/test.wxml-->
<view class="container">
  <view class="header">
    <text class="title">🔗 网络连接测试</text>
    <text class="subtitle">测试小程序与后端的连接状态</text>
  </view>

  <view class="config-info">
    <text class="config-title">当前配置</text>
    <text class="config-item">API地址: {{baseUrl}}</text>
    <text class="config-item">后端端口: 8080</text>
  </view>

  <view class="test-section">
    <view class="section-header">
      <text class="section-title">测试结果</text>
      <button class="retry-btn" bindtap="onRetryTest" disabled="{{isLoading}}">
        {{isLoading ? '测试中...' : '重新测试'}}
      </button>
    </view>

    <view class="loading" wx:if="{{isLoading}}">
      <view class="loading-spinner"></view>
      <text>正在测试连接...</text>
    </view>

    <view class="test-results" wx:if="{{testResults.length > 0}}">
      <view 
        class="test-item {{item.status}}"
        wx:for="{{testResults}}"
        wx:key="index"
        bindtap="onViewDetails"
        data-index="{{index}}"
      >
        <view class="test-header">
          <text class="test-name">{{item.name}}</text>
          <text class="test-status">
            {{item.status === 'success' ? '✅' : '❌'}}
          </text>
        </view>
        <text class="test-message">{{item.message}}</text>
      </view>
    </view>

    <view class="empty-state" wx:if="{{!isLoading && testResults.length === 0}}">
      <text class="empty-text">暂无测试结果</text>
      <button class="start-test-btn" bindtap="onRetryTest">开始测试</button>
    </view>
  </view>

  <view class="help-section">
    <text class="help-title">💡 连接问题排查</text>
    <view class="help-list">
      <text class="help-item">1. 确保后端服务正在运行 (localhost:8080)</text>
      <text class="help-item">2. 开启"不校验合法域名"选项</text>
      <text class="help-item">3. 清除微信开发者工具缓存</text>
      <text class="help-item">4. 检查防火墙和网络设置</text>
    </view>
  </view>
</view>
