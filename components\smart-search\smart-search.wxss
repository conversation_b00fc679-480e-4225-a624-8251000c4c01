/* components/smart-search/smart-search.wxss */
.smart-search {
  position: relative;
  z-index: 100;
}

.search-input-container {
  padding: 20rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
}

.search-input {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.9);
  border: 2rpx solid rgba(255, 107, 53, 0.2);
  border-radius: 50rpx;
  padding: 20rpx 30rpx;
  transition: all 0.3s ease;
  backdrop-filter: blur(10rpx);
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.search-input.focused {
  border-color: #ff6b35;
  box-shadow: 0 6rpx 30rpx rgba(255, 107, 53, 0.2);
  transform: translateY(-2rpx);
}

.search-icon {
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #ff6b35;
}

.input {
  flex: 1;
  font-size: 28rpx;
  color: #2f3542;
}

.clear-btn {
  width: 40rpx;
  height: 40rpx;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
  transition: all 0.3s ease;
}

.clear-btn:active {
  background: rgba(0, 0, 0, 0.2);
  transform: scale(0.9);
}

.clear-icon {
  font-size: 24rpx;
  color: #666;
}

.search-panel {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-top: none;
  max-height: 600rpx;
  overflow-y: auto;
  z-index: 99;
}

.search-panel.show {
  opacity: 1;
  transform: translateY(0);
}

.search-panel.hide {
  opacity: 0;
  transform: translateY(-20rpx);
}

.section-title {
  display: flex;
  align-items: center;
  padding: 24rpx 30rpx 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.title-icon {
  font-size: 28rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 26rpx;
  font-weight: 600;
  color: #2f3542;
  flex: 1;
}

.clear-history {
  font-size: 24rpx;
  color: #ff6b35;
  padding: 8rpx 16rpx;
  border: 1rpx solid #ff6b35;
  border-radius: 20rpx;
  transition: all 0.3s ease;
}

.clear-history:active {
  background: #ff6b35;
  color: white;
}

/* 搜索建议 */
.suggestions-list {
  padding: 0 30rpx 20rpx;
}

.suggestion-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:active {
  background: rgba(255, 107, 53, 0.1);
  transform: translateX(10rpx);
}

.suggestion-icon {
  font-size: 24rpx;
  color: #999;
  margin-right: 20rpx;
}

.suggestion-text {
  font-size: 28rpx;
  color: #2f3542;
}

/* 搜索历史 */
.history-list {
  padding: 0 30rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.history-item {
  background: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  border: 1rpx solid rgba(255, 107, 53, 0.2);
  transition: all 0.3s ease;
}

.history-item:active {
  background: #ff6b35;
  color: white;
  transform: scale(0.95);
}

.history-text {
  font-size: 26rpx;
}

/* 热门关键词 */
.hot-keywords-list {
  padding: 0 30rpx 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.hot-keyword-item {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  padding: 12rpx 24rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 53, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.hot-keyword-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.hot-keyword-item:active::before {
  left: 100%;
}

.hot-keyword-item:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 53, 0.3);
}

.hot-keyword-text {
  font-size: 26rpx;
  font-weight: 500;
  position: relative;
  z-index: 1;
}

/* 遮罩层 */
.search-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 98;
  transition: opacity 0.3s ease;
}

.search-mask.show {
  opacity: 1;
}

.search-mask.hide {
  opacity: 0;
}
