<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>生成演示图片</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .image-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #eee;
            border-radius: 10px;
            background: #f9f9f9;
        }
        canvas {
            border: 1px solid #ddd;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        .download-btn {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .download-btn:hover {
            transform: translateY(-2px);
        }
        .generate-all {
            text-align: center;
            margin-top: 30px;
        }
        .generate-all button {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 生成演示图片</h1>
        <p style="text-align: center; color: #666; margin-bottom: 30px;">
            点击下方按钮生成演示图片，然后右键保存到对应的文件夹中
        </p>
        
        <div class="image-grid" id="imageGrid"></div>
        
        <div class="generate-all">
            <button onclick="generateAllImages()">🎨 生成所有图片</button>
        </div>
    </div>

    <script>
        const images = [
            // 菜品图片
            { name: 'gongbao-chicken.jpg', title: '宫保鸡丁', emoji: '🍗', color: '#ff6b35', width: 400, height: 300 },
            { name: 'mapo-tofu.jpg', title: '麻婆豆腐', emoji: '🍲', color: '#e74c3c', width: 400, height: 300 },
            { name: 'braised-pork.jpg', title: '红烧肉', emoji: '🥩', color: '#8b4513', width: 400, height: 300 },
            { name: 'steamed-fish.jpg', title: '清蒸鲈鱼', emoji: '🐟', color: '#3498db', width: 400, height: 300 },
            { name: 'potato-strips.jpg', title: '酸辣土豆丝', emoji: '🥔', color: '#f39c12', width: 400, height: 300 },
            { name: 'sweet-sour-pork.jpg', title: '糖醋里脊', emoji: '🍖', color: '#e91e63', width: 400, height: 300 },
            { name: 'garlic-broccoli.jpg', title: '蒜蓉西兰花', emoji: '🥦', color: '#2ecc71', width: 400, height: 300 },
            { name: 'boiled-fish.jpg', title: '水煮鱼', emoji: '🍜', color: '#ff4757', width: 400, height: 300 },
            
            // 轮播图
            { name: 'banner1.jpg', title: '新品上市', emoji: '🆕', color: '#667eea', width: 750, height: 360 },
            { name: 'banner2.jpg', title: '限时优惠', emoji: '💥', color: '#f093fb', width: 750, height: 360 },
            { name: 'banner3.jpg', title: '招牌菜品', emoji: '👑', color: '#4facfe', width: 750, height: 360 },
            
            // 分类图标
            { name: 'hot.png', title: '热门', emoji: '🔥', color: '#ff6b35', width: 80, height: 80 },
            { name: 'meat.png', title: '肉类', emoji: '🥩', color: '#e74c3c', width: 80, height: 80 },
            { name: 'seafood.png', title: '海鲜', emoji: '🦐', color: '#3498db', width: 80, height: 80 },
            { name: 'vegetarian.png', title: '素食', emoji: '🥬', color: '#2ecc71', width: 80, height: 80 },
            { name: 'soup.png', title: '汤品', emoji: '🍲', color: '#f39c12', width: 80, height: 80 },
            { name: 'dessert.png', title: '甜品', emoji: '🍰', color: '#e91e63', width: 80, height: 80 },
            { name: 'drink.png', title: '饮品', emoji: '🥤', color: '#9c27b0', width: 80, height: 80 },
            { name: 'staple.png', title: '主食', emoji: '🍚', color: '#795548', width: 80, height: 80 }
        ];

        function createImage(config) {
            const canvas = document.createElement('canvas');
            canvas.width = config.width;
            canvas.height = config.height;
            const ctx = canvas.getContext('2d');

            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, config.width, config.height);
            gradient.addColorStop(0, config.color);
            gradient.addColorStop(1, adjustBrightness(config.color, -20));
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, config.width, config.height);

            // 添加纹理
            ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
            for (let i = 0; i < config.width; i += 20) {
                for (let j = 0; j < config.height; j += 20) {
                    if ((i + j) % 40 === 0) {
                        ctx.fillRect(i, j, 10, 10);
                    }
                }
            }

            // 添加emoji
            const emojiSize = Math.min(config.width, config.height) * 0.3;
            ctx.font = `${emojiSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.fillText(config.emoji, config.width / 2, config.height / 2);

            // 添加标题
            const titleSize = Math.min(config.width, config.height) * 0.08;
            ctx.font = `bold ${titleSize}px Arial`;
            ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            ctx.fillText(config.title, config.width / 2, config.height * 0.8);

            return canvas;
        }

        function adjustBrightness(hex, percent) {
            const num = parseInt(hex.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        function downloadCanvas(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        function generateAllImages() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';

            images.forEach(config => {
                const canvas = createImage(config);
                
                const item = document.createElement('div');
                item.className = 'image-item';
                
                const title = document.createElement('h3');
                title.textContent = config.title;
                title.style.margin = '0 0 10px 0';
                title.style.color = '#2c3e50';
                
                const size = document.createElement('p');
                size.textContent = `${config.width}x${config.height}`;
                size.style.margin = '0 0 10px 0';
                size.style.color = '#7f8c8d';
                size.style.fontSize = '12px';
                
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'download-btn';
                downloadBtn.textContent = '下载图片';
                downloadBtn.onclick = () => downloadCanvas(canvas, config.name);
                
                item.appendChild(title);
                item.appendChild(canvas);
                item.appendChild(size);
                item.appendChild(downloadBtn);
                
                grid.appendChild(item);
            });
        }

        // 页面加载时自动生成图片
        window.onload = generateAllImages;
    </script>
</body>
</html>
