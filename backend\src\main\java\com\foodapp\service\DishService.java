package com.foodapp.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.foodapp.entity.Dish;

import java.util.List;
import java.util.Map;

/**
 * 菜品服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface DishService {

    /**
     * 分页查询菜品
     */
    IPage<Dish> page(Integer current, Integer size, Long categoryId, String keyword, String sortBy, Boolean isHot, Boolean isNew);

    /**
     * 根据ID查询菜品详情
     */
    Dish getDetailById(Long id);

    /**
     * 获取热门菜品
     */
    List<Dish> getHotDishes(Integer limit);

    /**
     * 获取新品推荐
     */
    List<Dish> getNewDishes(Integer limit);

    /**
     * 获取推荐菜品
     */
    List<Dish> getRecommendDishes(Integer limit);

    /**
     * 搜索菜品
     */
    List<Dish> searchDishes(String keyword, Integer limit);

    /**
     * 根据分类获取菜品
     */
    List<Dish> getDishesByCategory(Long categoryId, Integer limit);

    /**
     * 获取菜品统计信息
     */
    Map<String, Object> getStats();

    /**
     * 增加菜品销量
     */
    void increaseSales(Long id, Integer quantity);

    /**
     * 更新菜品评分
     */
    void updateRating(Long id, Double rating);
}
