/* components/loading-animation/loading-animation.wxss */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(10rpx);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  transition: all 0.3s ease;
}

.loading-overlay.show {
  opacity: 1;
  visibility: visible;
}

.loading-overlay.hide {
  opacity: 0;
  visibility: hidden;
}

.loading-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 60rpx 80rpx;
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.3);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 30rpx;
}

/* 美食主题动画 */
.food-animation {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);
}

.food-icon {
  font-size: 60rpx;
  color: white;
}

.loading-dots {
  display: flex;
  gap: 12rpx;
}

.dot {
  width: 12rpx;
  height: 12rpx;
  background: #ff6b35;
  border-radius: 50%;
  animation: dotBounce 1.4s infinite ease-in-out both;
}

.dot-1 {
  animation-delay: -0.32s;
}

.dot-2 {
  animation-delay: -0.16s;
}

@keyframes dotBounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 脉冲动画 */
.pulse-animation {
  width: 120rpx;
  height: 120rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.pulse-circle {
  width: 80rpx;
  height: 80rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  box-shadow: 0 0 0 0 rgba(255, 107, 53, 0.7);
}

/* 波浪动画 */
.wave-animation {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.wave {
  width: 8rpx;
  height: 60rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 4rpx;
  animation: waveStretch 1.2s infinite ease-in-out;
}

.wave-1 {
  animation-delay: -1.1s;
}

.wave-2 {
  animation-delay: -1.0s;
}

.wave-3 {
  animation-delay: -0.9s;
}

.wave-4 {
  animation-delay: -0.8s;
}

@keyframes waveStretch {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1.0);
  }
}

/* 弹跳点动画 */
.dots-animation {
  display: flex;
  gap: 16rpx;
}

.bounce-dot {
  width: 20rpx;
  height: 20rpx;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  border-radius: 50%;
  animation: bounceDot 1.4s infinite ease-in-out both;
}

.bounce-dot-1 {
  animation-delay: -0.32s;
}

.bounce-dot-2 {
  animation-delay: -0.16s;
}

@keyframes bounceDot {
  0%, 80%, 100% {
    transform: scale(0) translateY(0);
  }
  40% {
    transform: scale(1) translateY(-30rpx);
  }
}

/* 加载文字 */
.loading-text {
  font-size: 28rpx;
  color: #2f3542;
  font-weight: 500;
  text-align: center;
  animation: textFade 2s infinite ease-in-out;
}

@keyframes textFade {
  0%, 100% {
    opacity: 0.6;
  }
  50% {
    opacity: 1;
  }
}
