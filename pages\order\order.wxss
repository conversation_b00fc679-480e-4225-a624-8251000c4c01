/* pages/order/order.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 通用区块样式 */
.delivery-section,
.order-items-section,
.remark-section,
.total-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
}

.item-count {
  font-size: 24rpx;
  color: #999;
}

/* 配送信息 */
.form-item {
  display: flex;
  align-items: flex-start;
  margin-bottom: 30rpx;
}

.form-item:last-child {
  margin-bottom: 0;
}

.form-label {
  width: 150rpx;
  font-size: 28rpx;
  color: #333;
  margin-right: 20rpx;
  margin-top: 10rpx;
  flex-shrink: 0;
}

.form-input {
  flex: 1;
  height: 60rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
}

.address-item {
  align-items: stretch;
}

.address-input {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.form-textarea {
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  min-height: 120rpx;
  margin-bottom: 20rpx;
}

.choose-address-btn {
  background-color: #ff6b35;
  color: white;
  border-radius: 8rpx;
  font-size: 24rpx;
  padding: 15rpx;
  border: none;
}

/* 订单商品 */
.order-items {
  display: flex;
  flex-direction: column;
}

.order-item {
  display: flex;
  align-items: center;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #eee;
}

.order-item:last-child {
  border-bottom: none;
}

.item-image {
  width: 100rpx;
  height: 100rpx;
  border-radius: 8rpx;
  margin-right: 20rpx;
}

.item-info {
  flex: 1;
  margin-right: 20rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.item-details {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.item-price {
  font-size: 24rpx;
  color: #ff6b35;
}

.item-quantity {
  font-size: 24rpx;
  color: #666;
}

.item-subtotal {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

/* 备注信息 */
.remark-input {
  width: 100%;
  min-height: 120rpx;
  background-color: #f8f9fa;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
}

/* 订单总计 */
.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.total-row:last-child {
  margin-bottom: 0;
}

.total-row.final {
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
  margin-top: 10rpx;
}

.total-label {
  font-size: 28rpx;
  color: #666;
}

.total-row.final .total-label {
  font-weight: bold;
  color: #333;
}

.total-value {
  font-size: 28rpx;
  color: #333;
}

.total-value.free {
  color: #2ed573;
}

.final-price {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.total-info {
  flex: 1;
}

.total-text {
  font-size: 24rpx;
  color: #666;
}

.total-amount {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-left: 10rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.back-btn,
.submit-btn {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
}

.back-btn {
  background-color: #f5f5f5;
  color: #666;
}

.submit-btn {
  background-color: #ff6b35;
  color: white;
}

.submit-btn.submitting {
  background-color: #ccc;
  color: #999;
}
