package com.foodapp.service;

import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.Map;

/**
 * 文件服务接口
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
public interface FileService {

    /**
     * 上传单个文件
     * 
     * @param file 文件
     * @param type 文件类型
     * @return 上传结果
     */
    Map<String, Object> uploadFile(MultipartFile file, String type) throws Exception;

    /**
     * 批量上传文件
     * 
     * @param files 文件数组
     * @param type 文件类型
     * @return 上传结果列表
     */
    List<Map<String, Object>> uploadFiles(MultipartFile[] files, String type) throws Exception;

    /**
     * 删除文件
     * 
     * @param url 文件URL
     */
    void deleteFile(String url) throws Exception;

    /**
     * 获取文件信息
     * 
     * @param url 文件URL
     * @return 文件信息
     */
    Map<String, Object> getFileInfo(String url) throws Exception;

    /**
     * 压缩图片
     * 
     * @param file 图片文件
     * @param quality 压缩质量
     * @param width 宽度
     * @param height 高度
     * @return 压缩结果
     */
    Map<String, Object> compressImage(MultipartFile file, float quality, Integer width, Integer height) throws Exception;

    /**
     * 生成缩略图
     * 
     * @param file 图片文件
     * @param width 宽度
     * @param height 高度
     * @return 缩略图结果
     */
    Map<String, Object> generateThumbnail(MultipartFile file, int width, int height) throws Exception;

    /**
     * 获取上传配置
     * 
     * @return 配置信息
     */
    Map<String, Object> getUploadConfig();
}
