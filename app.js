// app.js
App({
  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 登录
    wx.login({
      success: res => {
        console.log('登录成功', res.code)
        // 发送 res.code 到后台换取 openId, sessionKey, unionId
      }
    })
  },

  globalData: {
    userInfo: null,
    baseUrl: 'http://localhost:8080/api', // 后端API地址
    token: null,
    cart: [], // 购物车数据
    totalPrice: 0 // 购物车总价
  },

  // 设置用户信息
  setUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    wx.setStorageSync('userInfo', userInfo)
  },

  // 获取用户信息
  getUserInfo() {
    if (this.globalData.userInfo) {
      return this.globalData.userInfo
    }
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      this.globalData.userInfo = userInfo
      return userInfo
    }
    return null
  },

  // 设置token
  setToken(token) {
    this.globalData.token = token
    wx.setStorageSync('token', token)
  },

  // 获取token
  getToken() {
    if (this.globalData.token) {
      return this.globalData.token
    }
    const token = wx.getStorageSync('token')
    if (token) {
      this.globalData.token = token
      return token
    }
    return null
  },

  // 清除登录信息
  clearAuth() {
    this.globalData.userInfo = null
    this.globalData.token = null
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
  },

  // 添加到购物车
  addToCart(item) {
    const cart = this.globalData.cart
    const existIndex = cart.findIndex(cartItem => cartItem.id === item.id)

    if (existIndex > -1) {
      cart[existIndex].quantity += item.quantity || 1
    } else {
      cart.push({
        ...item,
        quantity: item.quantity || 1
      })
    }

    this.updateCartTotal()
    wx.setStorageSync('cart', cart)
  },

  // 从购物车移除
  removeFromCart(itemId) {
    const cart = this.globalData.cart
    const index = cart.findIndex(item => item.id === itemId)
    if (index > -1) {
      cart.splice(index, 1)
      this.updateCartTotal()
      wx.setStorageSync('cart', cart)
    }
  },

  // 更新购物车商品数量
  updateCartQuantity(itemId, quantity) {
    const cart = this.globalData.cart
    const item = cart.find(item => item.id === itemId)
    if (item) {
      if (quantity <= 0) {
        this.removeFromCart(itemId)
      } else {
        item.quantity = quantity
        this.updateCartTotal()
        wx.setStorageSync('cart', cart)
      }
    }
  },

  // 更新购物车总价
  updateCartTotal() {
    const cart = this.globalData.cart
    this.globalData.totalPrice = cart.reduce((total, item) => {
      return total + (item.price * item.quantity)
    }, 0)
  },

  // 清空购物车
  clearCart() {
    this.globalData.cart = []
    this.globalData.totalPrice = 0
    wx.removeStorageSync('cart')
  },

  // 获取购物车数据
  getCartData() {
    const cart = wx.getStorageSync('cart') || []
    this.globalData.cart = cart
    this.updateCartTotal()
    return {
      cart: this.globalData.cart,
      totalPrice: this.globalData.totalPrice
    }
  }
})
