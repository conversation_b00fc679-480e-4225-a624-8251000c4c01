// 简单的Node.js演示服务器
const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

const PORT = 8080;

// 创建上传目录
const uploadDirs = ['uploads', 'uploads/dishes', 'uploads/banners', 'uploads/categories', 'uploads/avatars', 'uploads/temp'];
uploadDirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        console.log(`✅ 创建目录: ${dir}`);
    }
});

// 模拟数据
const mockData = {
    dishes: [
        {
            id: 1,
            name: '宫保鸡丁',
            description: '经典川菜，鸡肉嫩滑，花生香脆，酸甜微辣',
            price: 28.00,
            originalPrice: 35.00,
            image: '/images/dishes/gongbao-chicken.jpg',
            categoryId: 2,
            tags: ['热门', '川菜', '下饭'],
            rating: 4.8,
            sales: 1256,
            isHot: true,
            isNew: false,
            discount: 0.8
        },
        {
            id: 2,
            name: '麻婆豆腐',
            description: '正宗川味，豆腐嫩滑，麻辣鲜香',
            price: 22.00,
            originalPrice: 22.00,
            image: '/images/dishes/mapo-tofu.jpg',
            categoryId: 4,
            tags: ['素食', '川菜', '经典'],
            rating: 4.6,
            sales: 892,
            isHot: true,
            isNew: false,
            discount: 1.0
        },
        {
            id: 3,
            name: '清蒸鲈鱼',
            description: '新鲜鲈鱼，肉质鲜嫩，营养丰富',
            price: 48.00,
            originalPrice: 48.00,
            image: '/images/dishes/steamed-fish.jpg',
            categoryId: 3,
            tags: ['海鲜', '清淡', '营养'],
            rating: 4.7,
            sales: 567,
            isHot: false,
            isNew: true,
            discount: 1.0
        },
        {
            id: 4,
            name: '蒜蓉西兰花',
            description: '清淡健康，营养丰富，蒜香浓郁',
            price: 18.00,
            originalPrice: 18.00,
            image: '/images/dishes/garlic-broccoli.jpg',
            categoryId: 4,
            tags: ['素食', '健康', '清淡'],
            rating: 4.4,
            sales: 456,
            isHot: false,
            isNew: true,
            discount: 1.0
        }
    ],
    categories: [
        { id: 1, name: '热门推荐', icon: '/images/categories/hot.png', color: '#ff6b35' },
        { id: 2, name: '肉类', icon: '/images/categories/meat.png', color: '#e74c3c' },
        { id: 3, name: '海鲜', icon: '/images/categories/seafood.png', color: '#3498db' },
        { id: 4, name: '素食', icon: '/images/categories/vegetarian.png', color: '#2ecc71' }
    ],
    banners: [
        { id: 1, title: '新品上市', subtitle: '精选美味，等你品尝', image: '/images/banners/banner1.jpg' },
        { id: 2, title: '限时优惠', subtitle: '全场8折，机不可失', image: '/images/banners/banner2.jpg' },
        { id: 3, title: '招牌菜品', subtitle: '经典口味，传承美食', image: '/images/banners/banner3.jpg' }
    ]
};

// 创建服务器
const server = http.createServer((req, res) => {
    const parsedUrl = url.parse(req.url, true);
    const pathname = parsedUrl.pathname;
    const method = req.method;

    // 设置CORS头
    res.setHeader('Access-Control-Allow-Origin', '*');
    res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

    // 处理OPTIONS请求
    if (method === 'OPTIONS') {
        res.writeHead(200);
        res.end();
        return;
    }

    // 设置响应头
    res.setHeader('Content-Type', 'application/json; charset=utf-8');

    try {
        // 路由处理
        if (pathname === '/' && method === 'GET') {
            // 首页
            const response = {
                code: 200,
                message: '🍽️ 美食小程序后端服务',
                data: {
                    status: 'running',
                    version: '1.0.0',
                    endpoints: [
                        'GET / - 首页',
                        'GET /api/health - 健康检查',
                        'GET /api/dish/page - 菜品列表',
                        'GET /api/dish/hot - 热门菜品',
                        'GET /api/categories - 分类列表',
                        'GET /api/banners - 轮播图',
                        'POST /api/file/upload - 文件上传'
                    ]
                },
                timestamp: Date.now()
            };
            res.writeHead(200);
            res.end(JSON.stringify(response, null, 2));

        } else if (pathname === '/api/health' && method === 'GET') {
            // 健康检查
            const response = {
                code: 200,
                message: '服务正常',
                data: { status: 'UP', timestamp: Date.now() }
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/dish/page' && method === 'GET') {
            // 菜品分页
            const response = {
                code: 200,
                message: '查询成功',
                data: {
                    records: mockData.dishes,
                    total: mockData.dishes.length,
                    current: 1,
                    size: 10
                }
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/dish/hot' && method === 'GET') {
            // 热门菜品
            const hotDishes = mockData.dishes.filter(dish => dish.isHot);
            const response = {
                code: 200,
                message: '查询成功',
                data: hotDishes
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/dish/new' && method === 'GET') {
            // 新品推荐
            const newDishes = mockData.dishes.filter(dish => dish.isNew);
            const response = {
                code: 200,
                message: '查询成功',
                data: newDishes
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/categories' && method === 'GET') {
            // 分类列表
            const response = {
                code: 200,
                message: '查询成功',
                data: mockData.categories
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/banners' && method === 'GET') {
            // 轮播图
            const response = {
                code: 200,
                message: '查询成功',
                data: mockData.banners
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname === '/api/file/upload' && method === 'POST') {
            // 文件上传
            const response = {
                code: 200,
                message: '上传成功',
                data: {
                    url: '/images/demo/uploaded-file.jpg',
                    fileName: 'uploaded-file.jpg',
                    size: 1024,
                    uploadTime: new Date().toISOString()
                }
            };
            res.writeHead(200);
            res.end(JSON.stringify(response));

        } else if (pathname.startsWith('/images/')) {
            // 静态图片服务
            const filePath = path.join(__dirname, 'uploads', pathname.replace('/images/', ''));
            if (fs.existsSync(filePath)) {
                const ext = path.extname(filePath).toLowerCase();
                const contentType = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.webp': 'image/webp'
                }[ext] || 'application/octet-stream';

                res.setHeader('Content-Type', contentType);
                res.writeHead(200);
                fs.createReadStream(filePath).pipe(res);
            } else {
                res.writeHead(404);
                res.end(JSON.stringify({ code: 404, message: '图片不存在' }));
            }

        } else {
            // 404
            const response = {
                code: 404,
                message: '接口不存在',
                data: null
            };
            res.writeHead(404);
            res.end(JSON.stringify(response));
        }

    } catch (error) {
        // 500
        const response = {
            code: 500,
            message: '服务器内部错误: ' + error.message,
            data: null
        };
        res.writeHead(500);
        res.end(JSON.stringify(response));
    }
});

// 启动服务器
server.listen(PORT, () => {
    console.log('=================================');
    console.log('🍽️  美食小程序后端启动成功！');
    console.log(`📱  访问地址: http://localhost:${PORT}`);
    console.log(`🖼️  图片访问: http://localhost:${PORT}/images/`);
    console.log(`📋  API接口: http://localhost:${PORT}/api/`);
    console.log('=================================');
});

// 错误处理
server.on('error', (err) => {
    console.error('❌ 服务器启动失败:', err.message);
});

process.on('SIGINT', () => {
    console.log('\n👋 服务器正在关闭...');
    server.close(() => {
        console.log('✅ 服务器已关闭');
        process.exit(0);
    });
});
