package com.foodapp.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.foodapp.common.Result;
import com.foodapp.entity.Dish;
import com.foodapp.service.DishService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 菜品控制器
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@RestController
@RequestMapping("/dish")
@CrossOrigin(origins = "*")
public class DishController {

    @Autowired
    private DishService dishService;

    /**
     * 分页查询菜品
     */
    @GetMapping("/page")
    public Result<IPage<Dish>> page(@RequestParam(defaultValue = "1") Integer current,
                                   @RequestParam(defaultValue = "10") Integer size,
                                   @RequestParam(required = false) Long categoryId,
                                   @RequestParam(required = false) String keyword,
                                   @RequestParam(required = false) String sortBy,
                                   @RequestParam(required = false) Boolean isHot,
                                   @RequestParam(required = false) Boolean isNew) {
        try {
            IPage<Dish> page = dishService.page(current, size, categoryId, keyword, sortBy, isHot, isNew);
            return Result.success(page);
        } catch (Exception e) {
            log.error("分页查询菜品失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询菜品详情
     */
    @GetMapping("/{id}")
    public Result<Dish> getById(@PathVariable Long id) {
        try {
            Dish dish = dishService.getDetailById(id);
            if (dish == null) {
                return Result.error("菜品不存在");
            }
            return Result.success(dish);
        } catch (Exception e) {
            log.error("查询菜品详情失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取热门菜品
     */
    @GetMapping("/hot")
    public Result<List<Dish>> getHotDishes(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Dish> dishes = dishService.getHotDishes(limit);
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("获取热门菜品失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取新品推荐
     */
    @GetMapping("/new")
    public Result<List<Dish>> getNewDishes(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Dish> dishes = dishService.getNewDishes(limit);
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("获取新品推荐失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取推荐菜品
     */
    @GetMapping("/recommend")
    public Result<List<Dish>> getRecommendDishes(@RequestParam(defaultValue = "10") Integer limit) {
        try {
            List<Dish> dishes = dishService.getRecommendDishes(limit);
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("获取推荐菜品失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 搜索菜品
     */
    @GetMapping("/search")
    public Result<List<Dish>> search(@RequestParam String keyword,
                                    @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<Dish> dishes = dishService.searchDishes(keyword, limit);
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("搜索菜品失败", e);
            return Result.error("搜索失败: " + e.getMessage());
        }
    }

    /**
     * 根据分类获取菜品
     */
    @GetMapping("/category/{categoryId}")
    public Result<List<Dish>> getByCategory(@PathVariable Long categoryId,
                                           @RequestParam(defaultValue = "20") Integer limit) {
        try {
            List<Dish> dishes = dishService.getDishesByCategory(categoryId, limit);
            return Result.success(dishes);
        } catch (Exception e) {
            log.error("根据分类获取菜品失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取菜品统计信息
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getStats() {
        try {
            Map<String, Object> stats = dishService.getStats();
            return Result.success(stats);
        } catch (Exception e) {
            log.error("获取菜品统计信息失败", e);
            return Result.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 增加菜品销量
     */
    @PostMapping("/{id}/sales")
    public Result<Void> increaseSales(@PathVariable Long id,
                                     @RequestParam(defaultValue = "1") Integer quantity) {
        try {
            dishService.increaseSales(id, quantity);
            return Result.success();
        } catch (Exception e) {
            log.error("增加菜品销量失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 更新菜品评分
     */
    @PostMapping("/{id}/rating")
    public Result<Void> updateRating(@PathVariable Long id,
                                    @RequestParam Double rating) {
        try {
            dishService.updateRating(id, rating);
            return Result.success();
        } catch (Exception e) {
            log.error("更新菜品评分失败", e);
            return Result.error("操作失败: " + e.getMessage());
        }
    }
}
