{"compileType": "miniprogram", "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "ignoreUploadUnusedFiles": true, "urlCheck": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "preloadBackgroundData": false, "minifyWXSS": true, "autoAudits": false, "newFeature": false, "uglifyFileName": false, "useIsolateContext": true, "nodeModules": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "packNpmManually": false, "enableEngineNative": false, "minifyWXML": true}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 4}, "libVersion": "3.8.6", "packOptions": {"ignore": [], "include": []}, "appid": "wxd95e0e94f816f2d3"}