// utils/icons.js - 图标工具类
class IconManager {
  constructor() {
    this.icons = {
      // 基础图标
      home: '🏠',
      search: '🔍',
      cart: '🛒',
      user: '👤',
      heart: '❤️',
      heartEmpty: '🤍',
      star: '⭐',
      starEmpty: '☆',
      
      // 食物图标
      food: '🍽️',
      pizza: '🍕',
      burger: '🍔',
      noodles: '🍜',
      rice: '🍚',
      chicken: '🍗',
      fish: '🐟',
      vegetable: '🥬',
      fruit: '🍎',
      drink: '🥤',
      coffee: '☕',
      tea: '🍵',
      
      // 状态图标
      hot: '🔥',
      new: '✨',
      sale: '💥',
      crown: '👑',
      gift: '🎁',
      medal: '🏅',
      trophy: '🏆',
      
      // 操作图标
      add: '➕',
      minus: '➖',
      close: '✖️',
      check: '✅',
      arrow: '➡️',
      arrowUp: '⬆️',
      arrowDown: '⬇️',
      arrowLeft: '⬅️',
      arrowRight: '➡️',
      
      // 表情图标
      smile: '😊',
      happy: '😄',
      love: '😍',
      cool: '😎',
      wink: '😉',
      thumbsUp: '👍',
      clap: '👏',
      fire: '🔥',
      
      // 天气图标
      sun: '☀️',
      moon: '🌙',
      cloud: '☁️',
      rain: '🌧️',
      snow: '❄️',
      
      // 时间图标
      clock: '🕐',
      calendar: '📅',
      alarm: '⏰',
      
      // 位置图标
      location: '📍',
      map: '🗺️',
      
      // 通讯图标
      phone: '📞',
      message: '💬',
      email: '📧',
      
      // 设置图标
      settings: '⚙️',
      theme: '🎨',
      language: '🌐',
      
      // 支付图标
      money: '💰',
      card: '💳',
      wallet: '👛',
      
      // 配送图标
      delivery: '🚚',
      bike: '🚲',
      scooter: '🛵',
      
      // 评价图标
      comment: '💭',
      review: '📝',
      rating: '⭐',
      
      // 优惠图标
      discount: '🏷️',
      coupon: '🎫',
      percent: '💯',
      
      // 分类图标
      category: '📂',
      tag: '🏷️',
      filter: '🔽',
      
      // 社交图标
      share: '📤',
      like: '👍',
      follow: '➕',
      
      // 安全图标
      lock: '🔒',
      unlock: '🔓',
      shield: '🛡️',
      
      // 通知图标
      bell: '🔔',
      notification: '📢',
      warning: '⚠️',
      info: 'ℹ️',
      
      // 媒体图标
      play: '▶️',
      pause: '⏸️',
      stop: '⏹️',
      record: '⏺️',
      
      // 文件图标
      file: '📄',
      folder: '📁',
      image: '🖼️',
      video: '🎥',
      
      // 工具图标
      tool: '🔧',
      hammer: '🔨',
      wrench: '🔧',
      
      // 运动图标
      sport: '⚽',
      run: '🏃',
      bike: '🚴',
      
      // 娱乐图标
      game: '🎮',
      music: '🎵',
      movie: '🎬',
      
      // 学习图标
      book: '📚',
      pen: '✏️',
      graduation: '🎓',
      
      // 健康图标
      health: '💊',
      hospital: '🏥',
      doctor: '👨‍⚕️',
      
      // 旅行图标
      plane: '✈️',
      train: '🚄',
      car: '🚗',
      hotel: '🏨',
      
      // 购物图标
      shop: '🛍️',
      bag: '👜',
      package: '📦',
      
      // 办公图标
      office: '🏢',
      computer: '💻',
      printer: '🖨️',
      
      // 自然图标
      tree: '🌳',
      flower: '🌸',
      mountain: '⛰️',
      ocean: '🌊'
    }
  }

  // 获取图标
  getIcon(name) {
    return this.icons[name] || '❓'
  }

  // 获取所有图标
  getAllIcons() {
    return this.icons
  }

  // 获取分类图标
  getCategoryIcons() {
    return {
      food: this.icons.food,
      pizza: this.icons.pizza,
      burger: this.icons.burger,
      noodles: this.icons.noodles,
      rice: this.icons.rice,
      chicken: this.icons.chicken,
      fish: this.icons.fish,
      vegetable: this.icons.vegetable,
      fruit: this.icons.fruit,
      drink: this.icons.drink,
      coffee: this.icons.coffee,
      tea: this.icons.tea
    }
  }

  // 获取状态图标
  getStatusIcons() {
    return {
      hot: this.icons.hot,
      new: this.icons.new,
      sale: this.icons.sale,
      crown: this.icons.crown,
      gift: this.icons.gift,
      medal: this.icons.medal,
      trophy: this.icons.trophy
    }
  }

  // 获取操作图标
  getActionIcons() {
    return {
      add: this.icons.add,
      minus: this.icons.minus,
      close: this.icons.close,
      check: this.icons.check,
      arrow: this.icons.arrow,
      arrowUp: this.icons.arrowUp,
      arrowDown: this.icons.arrowDown,
      arrowLeft: this.icons.arrowLeft,
      arrowRight: this.icons.arrowRight
    }
  }

  // 获取表情图标
  getEmotionIcons() {
    return {
      smile: this.icons.smile,
      happy: this.icons.happy,
      love: this.icons.love,
      cool: this.icons.cool,
      wink: this.icons.wink,
      thumbsUp: this.icons.thumbsUp,
      clap: this.icons.clap,
      fire: this.icons.fire
    }
  }

  // 根据评分获取星星图标
  getStarRating(rating, maxRating = 5) {
    let stars = ''
    for (let i = 1; i <= maxRating; i++) {
      if (i <= rating) {
        stars += this.icons.star
      } else {
        stars += this.icons.starEmpty
      }
    }
    return stars
  }

  // 根据热度获取火焰图标
  getHotLevel(level) {
    const fires = this.icons.fire.repeat(Math.min(level, 5))
    return fires
  }

  // 获取随机图标
  getRandomIcon() {
    const iconNames = Object.keys(this.icons)
    const randomName = iconNames[Math.floor(Math.random() * iconNames.length)]
    return this.icons[randomName]
  }

  // 检查图标是否存在
  hasIcon(name) {
    return this.icons.hasOwnProperty(name)
  }

  // 添加自定义图标
  addIcon(name, icon) {
    this.icons[name] = icon
  }

  // 移除图标
  removeIcon(name) {
    delete this.icons[name]
  }
}

// 创建全局图标管理器实例
const iconManager = new IconManager()

module.exports = {
  iconManager,
  IconManager
}
