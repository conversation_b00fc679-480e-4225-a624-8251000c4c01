version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: food-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: food_miniprogram
      MYSQL_USER: foodapp
      MYSQL_PASSWORD: foodapp123
      TZ: Asia/Shanghai
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./src/main/resources/sql/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - food-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: food-redis
    restart: always
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    networks:
      - food-network

  # 后端应用
  app:
    build: .
    container_name: food-backend
    restart: always
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ******************************************************************************************************************************************************
      SPRING_DATASOURCE_USERNAME: foodapp
      SPRING_DATASOURCE_PASSWORD: foodapp123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    volumes:
      - ./uploads:/app/uploads
      - app_logs:/app/logs
    depends_on:
      - mysql
      - redis
    networks:
      - food-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: food-nginx
    restart: always
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./uploads:/usr/share/nginx/html/images
      - nginx_logs:/var/log/nginx
    depends_on:
      - app
    networks:
      - food-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  food-network:
    driver: bridge
