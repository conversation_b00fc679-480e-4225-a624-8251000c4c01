<!--components/loading-animation/loading-animation.wxml-->
<view class="loading-overlay {{show ? 'show' : 'hide'}}" wx:if="{{show}}">
  <view class="loading-container">
    <!-- 美食主题加载动画 -->
    <view class="loading-content" wx:if="{{type === 'food'}}">
      <view class="food-animation" animation="{{animationData}}">
        <text class="food-icon">🍽️</text>
      </view>
      <view class="loading-dots">
        <view class="dot dot-1"></view>
        <view class="dot dot-2"></view>
        <view class="dot dot-3"></view>
      </view>
      <text class="loading-text">{{text}}</text>
    </view>

    <!-- 脉冲动画 -->
    <view class="loading-content" wx:elif="{{type === 'pulse'}}">
      <view class="pulse-animation" animation="{{animationData}}">
        <view class="pulse-circle"></view>
      </view>
      <text class="loading-text">{{text}}</text>
    </view>

    <!-- 波浪动画 -->
    <view class="loading-content" wx:elif="{{type === 'wave'}}">
      <view class="wave-animation">
        <view class="wave wave-1"></view>
        <view class="wave wave-2"></view>
        <view class="wave wave-3"></view>
        <view class="wave wave-4"></view>
      </view>
      <text class="loading-text">{{text}}</text>
    </view>

    <!-- 点点动画 -->
    <view class="loading-content" wx:else>
      <view class="dots-animation">
        <view class="bounce-dot bounce-dot-1"></view>
        <view class="bounce-dot bounce-dot-2"></view>
        <view class="bounce-dot bounce-dot-3"></view>
      </view>
      <text class="loading-text">{{text}}</text>
    </view>
  </view>
</view>
