<!--pages/detail/detail.wxml-->
<view class="container" wx:if="{{!loading && dish}}">
  <!-- 菜品图片 -->
  <view class="image-section">
    <image 
      class="dish-image" 
      src="{{dish.image || '/images/dish-default.jpg'}}" 
      mode="aspectFill"
      bindtap="onImageTap"
    />
    <view class="image-overlay">
      <view class="tags">
        <text class="tag hot" wx:if="{{dish.is_hot}}">热门</text>
        <text class="tag new" wx:if="{{dish.is_new}}">新品</text>
      </view>
    </view>
  </view>

  <!-- 菜品信息 -->
  <view class="info-section">
    <view class="dish-header">
      <text class="dish-name">{{dish.name}}</text>
      <text class="dish-price">¥{{formatPrice(dish.price)}}</text>
    </view>
    
    <view class="dish-meta">
      <text class="category" bindtap="onCategoryTap">{{dish.category_name}}</text>
      <text class="sales">已售{{dish.sales_count}}</text>
    </view>
    
    <view class="dish-description">
      <text class="desc-title">菜品介绍</text>
      <text class="desc-content">{{dish.description || '暂无介绍'}}</text>
    </view>
  </view>

  <!-- 数量选择 -->
  <view class="quantity-section">
    <text class="quantity-label">数量</text>
    <view class="quantity-control">
      <view 
        class="quantity-btn decrease {{quantity <= 1 ? 'disabled' : ''}}" 
        bindtap="onDecreaseQuantity"
      >
        <text>-</text>
      </view>
      <input 
        class="quantity-input" 
        type="number" 
        value="{{quantity}}"
        bindinput="onQuantityInput"
      />
      <view 
        class="quantity-btn increase" 
        bindtap="onIncreaseQuantity"
      >
        <text>+</text>
      </view>
    </view>
  </view>

  <!-- 相关推荐 -->
  <view class="related-section" wx:if="{{relatedDishes.length > 0}}">
    <view class="section-title">相关推荐</view>
    <scroll-view class="related-scroll" scroll-x="{{true}}">
      <view class="related-list">
        <view 
          class="related-item" 
          wx:for="{{relatedDishes}}" 
          wx:key="id"
          bindtap="onRelatedDishTap"
          data-item="{{item}}"
        >
          <image 
            class="related-image" 
            src="{{item.image || '/images/dish-default.jpg'}}" 
            mode="aspectFill"
          />
          <text class="related-name">{{item.name}}</text>
          <text class="related-price">¥{{formatPrice(item.price)}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 底部操作栏 -->
  <view class="bottom-bar">
    <view class="total-info">
      <text class="total-label">小计：</text>
      <text class="total-price">¥{{formatPrice(dish.price * quantity)}}</text>
    </view>
    <view class="action-buttons">
      <button class="cart-btn" bindtap="onAddToCart">
        加入购物车
      </button>
      <button class="buy-btn" bindtap="onBuyNow">
        立即购买
      </button>
    </view>
  </view>
</view>

<!-- 加载状态 -->
<view class="loading-container" wx:if="{{loading}}">
  <view class="loading">
    <text>加载中...</text>
  </view>
</view>

<!-- 错误状态 -->
<view class="error-container" wx:if="{{!loading && !dish}}">
  <view class="error">
    <text>菜品不存在或已下架</text>
  </view>
</view>
