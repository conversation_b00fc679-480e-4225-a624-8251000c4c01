# 📱 微信小程序网络配置指南

## 🔧 问题诊断

如果小程序无法连接后端，请按以下步骤检查：

### 1. 后端服务状态 ✅
- **服务地址**: http://localhost:8080
- **状态**: 正在运行
- **API接口**: 全部正常

### 2. 微信开发者工具配置

#### 步骤1: 开启本地调试
1. 打开微信开发者工具
2. 点击右上角的"详情"按钮
3. 在"本地设置"中勾选：
   - ✅ **不校验合法域名、web-view（业务域名）、TLS 版本以及 HTTPS 证书**
   - ✅ **开启调试模式**

#### 步骤2: 检查网络权限
1. 在"项目配置"中确认：
   - ✅ **启用网络调试**
   - ✅ **允许HTTP请求**

#### 步骤3: 清除缓存
1. 点击"清缓存" → "清除全部缓存"
2. 重新编译项目

### 3. 项目配置检查

#### project.config.json
确保包含以下配置：
```json
{
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "minified": true
  }
}
```

#### app.js 配置
当前API配置：
```javascript
globalData: {
  baseUrl: 'http://localhost:8080/api'
}
```

### 4. 网络测试步骤

#### 方法1: 在小程序中测试
在小程序的控制台中运行：
```javascript
wx.request({
  url: 'http://localhost:8080/api/health',
  method: 'GET',
  success: (res) => {
    console.log('连接成功:', res.data);
  },
  fail: (err) => {
    console.error('连接失败:', err);
  }
});
```

#### 方法2: 使用测试页面
打开 `test-connection.html` 测试所有API接口

### 5. 常见问题解决

#### 问题1: 请求被拦截
**症状**: 控制台显示"不在以下 request 合法域名列表中"
**解决**: 确保已关闭域名校验

#### 问题2: 连接超时
**症状**: 请求一直pending或超时
**解决**: 
1. 检查防火墙设置
2. 确认后端服务正在运行
3. 尝试重启微信开发者工具

#### 问题3: CORS错误
**症状**: 跨域请求被阻止
**解决**: 后端已配置CORS，如仍有问题请重启后端服务

#### 问题4: 端口被占用
**症状**: 后端无法启动
**解决**: 
```bash
# 检查端口占用
netstat -ano | findstr :8080
# 或修改后端端口
```

### 6. 调试技巧

#### 开启详细日志
在小程序中添加调试代码：
```javascript
// 在 app.js 中添加
wx.setEnableDebug({
  enableDebug: true
});

// 在请求前添加日志
console.log('发起请求:', url);
```

#### 使用网络面板
1. 打开微信开发者工具的"Network"面板
2. 查看请求状态和响应内容
3. 检查请求头和响应头

### 7. 备用方案

如果本地连接仍有问题，可以：

#### 方案1: 使用内网IP
```javascript
// 获取本机IP地址
ipconfig
// 将localhost替换为实际IP
baseUrl: 'http://*************:8080/api'
```

#### 方案2: 使用代理服务
```javascript
// 使用ngrok等工具创建公网隧道
ngrok http 8080
```

#### 方案3: 模拟数据
临时使用本地模拟数据进行开发

### 8. 验证步骤

完成配置后，按顺序验证：

1. ✅ 后端服务正常运行
2. ✅ 测试页面可以访问API
3. ✅ 微信开发者工具配置正确
4. ✅ 小程序可以发起网络请求
5. ✅ 数据正常显示

### 9. 联系支持

如果问题仍然存在：
1. 检查微信开发者工具版本
2. 查看控制台错误信息
3. 提供详细的错误日志

---

🔧 **配置完成后，小程序应该能够正常连接后端服务！**
