# 服务器配置
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true
      force: true

# Spring配置
spring:
  application:
    name: food-miniprogram-backend

  # 数据源配置 - 使用H2内存数据库进行演示
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:food_miniprogram;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password:

  # H2控制台配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # Redis配置 (暂时禁用)
  # redis:
  #   host: localhost
  #   port: 6379
  #   password:
  #   database: 0

  # 文件上传配置
  servlet:
    multipart:
      enabled: true
      max-file-size: 10MB
      max-request-size: 100MB
      file-size-threshold: 2KB

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
    default-property-inclusion: non_null

# MyBatis Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: ASSIGN_ID
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
  mapper-locations: classpath*:mapper/**/*Mapper.xml

# 日志配置
logging:
  level:
    com.foodapp: debug
    org.springframework.security: debug
  pattern:
    console: '%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n'

# 自定义配置
food-app:
  # JWT配置
  jwt:
    secret: foodapp2024miniprogram
    expiration: 604800 # 7天
    header: Authorization
    prefix: Bearer

  # 文件上传配置
  upload:
    # 上传路径
    path: /uploads/
    # 访问路径
    access-path: /images/**
    # 允许的文件类型
    allowed-types:
      - jpg
      - jpeg
      - png
      - gif
      - webp
    # 最大文件大小(MB)
    max-size: 10

  # 微信小程序配置
  wechat:
    app-id: your-app-id
    app-secret: your-app-secret

  # 分页配置
  page:
    default-size: 10
    max-size: 100
