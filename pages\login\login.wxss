/* pages/login/login.wxss */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  padding: 100rpx 60rpx 60rpx;
  display: flex;
  flex-direction: column;
}

/* 头部 */
.header {
  text-align: center;
  margin-bottom: 100rpx;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

/* 登录方式切换 */
.login-tabs {
  display: flex;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50rpx;
  padding: 8rpx;
  margin-bottom: 60rpx;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  transition: all 0.3s;
}

.tab-item.active {
  background-color: white;
  color: #ff6b35;
  font-weight: bold;
}

/* 登录表单 */
.login-form {
  flex: 1;
}

.input-group {
  margin-bottom: 40rpx;
}

.input-label {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label-icon {
  font-size: 32rpx;
  margin-right: 15rpx;
}

.label-text {
  font-size: 28rpx;
  color: white;
  font-weight: bold;
}

.input-field {
  width: 100%;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50rpx;
  padding: 0 40rpx;
  font-size: 28rpx;
  border: none;
}

.code-input {
  display: flex;
  align-items: center;
}

.code-input .input-field {
  flex: 1;
  margin-right: 20rpx;
}

.code-btn {
  width: 200rpx;
  height: 100rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2rpx solid rgba(255, 255, 255, 0.5);
  border-radius: 50rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.code-btn.disabled {
  background-color: rgba(255, 255, 255, 0.1);
  color: rgba(255, 255, 255, 0.5);
  border-color: rgba(255, 255, 255, 0.3);
}

.login-btn {
  width: 100%;
  height: 100rpx;
  background-color: white;
  color: #ff6b35;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  margin-top: 40rpx;
}

.login-btn[disabled] {
  background-color: rgba(255, 255, 255, 0.7);
  color: rgba(255, 107, 53, 0.7);
}

/* 微信登录 */
.wechat-login {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.wechat-info {
  text-align: center;
  margin-bottom: 80rpx;
}

.wechat-icon {
  font-size: 120rpx;
  display: block;
  margin-bottom: 40rpx;
}

.wechat-title {
  display: block;
  font-size: 36rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.wechat-desc {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.wechat-btn {
  width: 100%;
  height: 100rpx;
  background-color: white;
  color: #ff6b35;
  border-radius: 50rpx;
  font-size: 32rpx;
  font-weight: bold;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.wechat-btn[disabled] {
  background-color: rgba(255, 255, 255, 0.7);
  color: rgba(255, 107, 53, 0.7);
}

.wechat-btn-icon {
  font-size: 36rpx;
  margin-right: 20rpx;
}

.wechat-btn-text {
  font-size: 32rpx;
}

/* 用户协议 */
.agreement {
  text-align: center;
  margin-top: 60rpx;
}

.agreement-text {
  font-size: 24rpx;
  color: rgba(255, 255, 255, 0.7);
  line-height: 1.5;
}

.agreement-link {
  color: white;
  text-decoration: underline;
}
