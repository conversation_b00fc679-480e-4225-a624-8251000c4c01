# 图标文件说明

## 当前使用方案
目前使用文字图标（Emoji）作为TabBar图标，这样可以：
- 立即可用，无需额外图片文件
- 色彩丰富，视觉效果好
- 文件体积小，加载快速
- 跨平台兼容性好

## 如需使用图片图标

如果您希望使用传统的PNG图标，请按以下规格准备图标文件：

### 图标规格要求
- **尺寸**: 81px × 81px (3倍图)
- **格式**: PNG格式，支持透明背景
- **颜色**: 
  - 未选中状态：灰色 (#999999)
  - 选中状态：橙色 (#ff6b35)

### 需要的图标文件
1. `images/home.png` - 首页图标（未选中）
2. `images/home-active.png` - 首页图标（选中）
3. `images/category.png` - 分类图标（未选中）
4. `images/category-active.png` - 分类图标（选中）
5. `images/cart.png` - 购物车图标（未选中）
6. `images/cart-active.png` - 购物车图标（选中）
7. `images/profile.png` - 个人中心图标（未选中）
8. `images/profile-active.png` - 个人中心图标（选中）

### 图标设计建议
- **首页**: 房子图标
- **分类**: 网格或列表图标
- **购物车**: 购物车图标
- **个人中心**: 用户头像图标

### 如何切换回图片图标

如果您准备好了图标文件，可以修改 `app.json` 中的 tabBar 配置：

```json
"tabBar": {
  "color": "#999999",
  "selectedColor": "#ff6b35",
  "backgroundColor": "#ffffff",
  "borderStyle": "black",
  "list": [
    {
      "pagePath": "pages/index/index",
      "text": "首页",
      "iconPath": "images/home.png",
      "selectedIconPath": "images/home-active.png"
    },
    {
      "pagePath": "pages/category/category",
      "text": "分类",
      "iconPath": "images/category.png",
      "selectedIconPath": "images/category-active.png"
    },
    {
      "pagePath": "pages/cart/cart",
      "text": "购物车",
      "iconPath": "images/cart.png",
      "selectedIconPath": "images/cart-active.png"
    },
    {
      "pagePath": "pages/profile/profile",
      "text": "我的",
      "iconPath": "images/profile.png",
      "selectedIconPath": "images/profile-active.png"
    }
  ]
}
```

## 推荐图标资源

### 免费图标网站
1. **Iconfont (阿里巴巴矢量图标库)**: https://www.iconfont.cn/
2. **Feather Icons**: https://feathericons.com/
3. **Heroicons**: https://heroicons.com/
4. **Tabler Icons**: https://tabler-icons.io/

### 图标制作工具
1. **Figma**: 免费的在线设计工具
2. **Sketch**: Mac平台的设计工具
3. **Adobe Illustrator**: 专业矢量图形设计软件

## 当前效果
使用文字图标的效果：
- 🏠 首页
- 📋 分类  
- 🛒 购物车
- 👤 我的

这种方式简洁明了，符合现代设计趋势，建议继续使用。
