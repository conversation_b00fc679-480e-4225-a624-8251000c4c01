// components/theme-selector/theme-selector.js
const { themeManager } = require('../../utils/theme')

Component({
  properties: {
    show: {
      type: Boolean,
      value: false
    }
  },

  data: {
    themes: [],
    currentTheme: 'default',
    animationData: {}
  },

  lifetimes: {
    attached() {
      this.loadThemes()
    }
  },

  observers: {
    'show': function(show) {
      this.toggleAnimation(show)
    }
  },

  methods: {
    loadThemes() {
      const themes = themeManager.getAllThemes()
      const currentTheme = themeManager.getCurrentTheme()
      
      this.setData({
        themes,
        currentTheme: currentTheme.name
      })
    },

    toggleAnimation(show) {
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease-out'
      })

      if (show) {
        animation.translateY(0).opacity(1).step()
      } else {
        animation.translateY(100).opacity(0).step()
      }

      this.setData({
        animationData: animation.export()
      })
    },

    onThemeSelect(e) {
      const { theme } = e.currentTarget.dataset
      
      // 切换主题
      const success = themeManager.switchTheme(theme.key)
      
      if (success) {
        this.setData({
          currentTheme: theme.key
        })
        
        wx.showToast({
          title: `已切换到${theme.name}主题`,
          icon: 'success'
        })
        
        // 触发主题变更事件
        this.triggerEvent('themeChange', { 
          theme: theme.key 
        })
        
        // 延迟关闭选择器
        setTimeout(() => {
          this.onClose()
        }, 1000)
      }
    },

    onClose() {
      this.triggerEvent('close')
    },

    onMaskTap() {
      this.onClose()
    },

    // 阻止事件冒泡
    onContentTap() {
      // 空函数，阻止点击内容区域时关闭
    }
  }
})
