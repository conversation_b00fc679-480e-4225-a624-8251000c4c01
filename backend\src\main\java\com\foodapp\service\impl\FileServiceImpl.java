package com.foodapp.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.foodapp.service.FileService;
import lombok.extern.slf4j.Slf4j;
import net.coobird.thumbnailator.Thumbnails;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 文件服务实现类
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Value("${food-app.upload.path:/uploads/}")
    private String uploadPath;

    @Value("${food-app.upload.allowed-types}")
    private List<String> allowedTypes;

    @Value("${food-app.upload.max-size:10}")
    private long maxSize;

    @Value("${server.port:8080}")
    private String serverPort;

    private String baseUrl;

    @PostConstruct
    public void init() {
        // 创建上传目录
        createUploadDirectories();
        // 设置基础URL
        baseUrl = "http://localhost:" + serverPort + "/api/images/";
    }

    /**
     * 创建上传目录
     */
    private void createUploadDirectories() {
        try {
            String[] directories = {"dishes", "banners", "categories", "avatars", "temp"};
            for (String dir : directories) {
                Path dirPath = Paths.get(uploadPath, dir);
                if (!Files.exists(dirPath)) {
                    Files.createDirectories(dirPath);
                    log.info("创建目录: {}", dirPath.toAbsolutePath());
                }
            }
        } catch (IOException e) {
            log.error("创建上传目录失败", e);
        }
    }

    @Override
    public Map<String, Object> uploadFile(MultipartFile file, String type) throws Exception {
        // 验证文件
        validateFile(file);
        
        // 生成文件名和路径
        String fileName = generateFileName(file.getOriginalFilename());
        String relativePath = generateRelativePath(type, fileName);
        String fullPath = uploadPath + relativePath;
        
        // 确保目录存在
        File targetFile = new File(fullPath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        // 保存文件
        file.transferTo(targetFile);
        
        // 生成访问URL
        String url = baseUrl + relativePath.replace("\\", "/");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("url", url);
        result.put("fileName", fileName);
        result.put("originalName", file.getOriginalFilename());
        result.put("size", file.getSize());
        result.put("type", type);
        result.put("uploadTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public List<Map<String, Object>> uploadFiles(MultipartFile[] files, String type) throws Exception {
        List<Map<String, Object>> results = new ArrayList<>();
        
        for (MultipartFile file : files) {
            if (!file.isEmpty()) {
                try {
                    Map<String, Object> result = uploadFile(file, type);
                    results.add(result);
                } catch (Exception e) {
                    log.error("文件上传失败: {}", file.getOriginalFilename(), e);
                    // 继续处理其他文件
                }
            }
        }
        
        return results;
    }

    @Override
    public void deleteFile(String url) throws Exception {
        if (StrUtil.isBlank(url)) {
            return;
        }
        
        // 从URL中提取相对路径
        String relativePath = url.replace(baseUrl, "");
        String fullPath = uploadPath + relativePath;
        
        File file = new File(fullPath);
        if (file.exists()) {
            if (file.delete()) {
                log.info("文件删除成功: {}", fullPath);
            } else {
                log.warn("文件删除失败: {}", fullPath);
            }
        } else {
            log.warn("文件不存在: {}", fullPath);
        }
    }

    @Override
    public Map<String, Object> getFileInfo(String url) throws Exception {
        String relativePath = url.replace(baseUrl, "");
        String fullPath = uploadPath + relativePath;
        
        File file = new File(fullPath);
        if (!file.exists()) {
            throw new RuntimeException("文件不存在");
        }
        
        Map<String, Object> info = new HashMap<>();
        info.put("url", url);
        info.put("fileName", file.getName());
        info.put("size", file.length());
        info.put("lastModified", new Date(file.lastModified()));
        info.put("exists", file.exists());
        
        return info;
    }

    @Override
    public Map<String, Object> compressImage(MultipartFile file, float quality, Integer width, Integer height) throws Exception {
        // 验证文件
        validateFile(file);
        validateImageFile(file);
        
        // 生成文件名和路径
        String fileName = "compressed_" + generateFileName(file.getOriginalFilename());
        String relativePath = generateRelativePath("temp", fileName);
        String fullPath = uploadPath + relativePath;
        
        // 确保目录存在
        File targetFile = new File(fullPath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        // 压缩图片
        Thumbnails.Builder<? extends File> builder = Thumbnails.of(file.getInputStream())
                .outputQuality(quality);
        
        if (width != null && height != null) {
            builder.size(width, height);
        } else {
            builder.scale(1.0);
        }
        
        builder.toFile(targetFile);
        
        // 生成访问URL
        String url = baseUrl + relativePath.replace("\\", "/");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("url", url);
        result.put("fileName", fileName);
        result.put("originalName", file.getOriginalFilename());
        result.put("originalSize", file.getSize());
        result.put("compressedSize", targetFile.length());
        result.put("quality", quality);
        result.put("uploadTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> generateThumbnail(MultipartFile file, int width, int height) throws Exception {
        // 验证文件
        validateFile(file);
        validateImageFile(file);
        
        // 生成文件名和路径
        String fileName = "thumb_" + width + "x" + height + "_" + generateFileName(file.getOriginalFilename());
        String relativePath = generateRelativePath("temp", fileName);
        String fullPath = uploadPath + relativePath;
        
        // 确保目录存在
        File targetFile = new File(fullPath);
        File parentDir = targetFile.getParentFile();
        if (!parentDir.exists()) {
            parentDir.mkdirs();
        }
        
        // 生成缩略图
        Thumbnails.of(file.getInputStream())
                .size(width, height)
                .keepAspectRatio(false)
                .toFile(targetFile);
        
        // 生成访问URL
        String url = baseUrl + relativePath.replace("\\", "/");
        
        // 返回结果
        Map<String, Object> result = new HashMap<>();
        result.put("url", url);
        result.put("fileName", fileName);
        result.put("originalName", file.getOriginalFilename());
        result.put("size", targetFile.length());
        result.put("width", width);
        result.put("height", height);
        result.put("uploadTime", LocalDateTime.now());
        
        return result;
    }

    @Override
    public Map<String, Object> getUploadConfig() {
        Map<String, Object> config = new HashMap<>();
        config.put("allowedTypes", allowedTypes);
        config.put("maxSize", maxSize);
        config.put("uploadPath", uploadPath);
        config.put("baseUrl", baseUrl);
        
        return config;
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) throws Exception {
        if (file == null || file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }
        
        if (file.getSize() > maxSize * 1024 * 1024) {
            throw new RuntimeException("文件大小超过限制: " + maxSize + "MB");
        }
        
        String fileName = file.getOriginalFilename();
        if (StrUtil.isBlank(fileName)) {
            throw new RuntimeException("文件名不能为空");
        }
        
        String extension = FileUtil.extName(fileName).toLowerCase();
        if (!allowedTypes.contains(extension)) {
            throw new RuntimeException("不支持的文件类型: " + extension);
        }
    }

    /**
     * 验证图片文件
     */
    private void validateImageFile(MultipartFile file) throws Exception {
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            throw new RuntimeException("文件不是有效的图片格式");
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String originalName) {
        String extension = FileUtil.extName(originalName);
        return IdUtil.simpleUUID() + "." + extension;
    }

    /**
     * 生成相对路径
     */
    private String generateRelativePath(String type, String fileName) {
        String dateStr = DateUtil.format(new Date(), "yyyy/MM/dd");
        return type + "/" + dateStr + "/" + fileName;
    }
}
