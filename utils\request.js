// 网络请求工具
const app = getApp()

/**
 * 封装wx.request
 * @param {Object} options 请求参数
 */
function request(options) {
  return new Promise((resolve, reject) => {
    const token = app.getToken()
    const fullUrl = app.globalData.baseUrl + options.url

    console.log('发起网络请求:', {
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data || {}
    })

    wx.request({
      url: fullUrl,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
        ...options.header
      },
      success: (res) => {
        console.log('网络请求成功:', {
          url: fullUrl,
          statusCode: res.statusCode,
          data: res.data
        })

        if (res.statusCode === 200) {
          // 兼容不同的响应格式
          if (res.data.code === 200 || res.data.code === 0 || res.data.code === undefined) {
            resolve(res.data)
          } else {
            console.error('业务错误:', res.data)
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // token过期，清除登录信息
          app.clearAuth()
          wx.showToast({
            title: '登录已过期，请重新登录',
            icon: 'none'
          })
          setTimeout(() => {
            wx.navigateTo({
              url: '/pages/login/login'
            })
          }, 1500)
          reject(res)
        } else {
          console.error('HTTP错误:', res)
          wx.showToast({
            title: `网络请求失败 (${res.statusCode})`,
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        console.error('网络请求失败:', {
          url: fullUrl,
          error: err
        })
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

/**
 * GET请求
 */
function get(url, data = {}) {
  return request({
    url,
    method: 'GET',
    data
  })
}

/**
 * POST请求
 */
function post(url, data = {}) {
  return request({
    url,
    method: 'POST',
    data
  })
}

/**
 * PUT请求
 */
function put(url, data = {}) {
  return request({
    url,
    method: 'PUT',
    data
  })
}

/**
 * DELETE请求
 */
function del(url, data = {}) {
  return request({
    url,
    method: 'DELETE',
    data
  })
}

/**
 * 上传文件
 */
function uploadFile(filePath, name = 'file', formData = {}) {
  return new Promise((resolve, reject) => {
    const token = app.getToken()

    wx.uploadFile({
      url: app.globalData.baseUrl + '/upload',
      filePath,
      name,
      formData,
      header: {
        'Authorization': token ? `Bearer ${token}` : ''
      },
      success: (res) => {
        const data = JSON.parse(res.data)
        if (data.code === 0) {
          resolve(data)
        } else {
          wx.showToast({
            title: data.message || '上传失败',
            icon: 'none'
          })
          reject(data)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '上传失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

module.exports = {
  request,
  get,
  post,
  put,
  del,
  uploadFile
}
