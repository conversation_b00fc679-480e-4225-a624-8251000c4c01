/* pages/test/test.wxss */
.container {
  padding: 30rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
}

.config-info {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.config-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.config-item {
  display: block;
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 10rpx;
  font-family: monospace;
}

.test-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2c3e50;
}

.retry-btn {
  background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 15rpx 30rpx;
  font-size: 26rpx;
}

.retry-btn[disabled] {
  background: #bdc3c7;
}

.loading {
  text-align: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #ff6b35;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.test-results {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.test-item {
  padding: 25rpx;
  border-radius: 15rpx;
  border-left: 6rpx solid;
  transition: all 0.3s ease;
}

.test-item.success {
  background: #d4edda;
  border-left-color: #28a745;
}

.test-item.error {
  background: #f8d7da;
  border-left-color: #dc3545;
}

.test-item:active {
  transform: scale(0.98);
}

.test-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10rpx;
}

.test-name {
  font-size: 30rpx;
  font-weight: bold;
  color: #2c3e50;
}

.test-status {
  font-size: 32rpx;
}

.test-message {
  font-size: 26rpx;
  color: #6c757d;
}

.empty-state {
  text-align: center;
  padding: 60rpx 0;
}

.empty-text {
  display: block;
  font-size: 28rpx;
  color: #6c757d;
  margin-bottom: 30rpx;
}

.start-test-btn {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  border: none;
  border-radius: 30rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

.help-section {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  padding: 30rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.help-title {
  display: block;
  font-size: 32rpx;
  font-weight: bold;
  color: white;
  margin-bottom: 20rpx;
}

.help-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.help-item {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.5;
}
