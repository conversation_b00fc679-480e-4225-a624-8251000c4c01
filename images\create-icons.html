<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TabBar图标生成器</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .icon-item {
            text-align: center;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        button {
            background: #ff6b35;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #e55a2b;
        }
        .instructions {
            background: #e8f4fd;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信小程序 TabBar 图标生成器</h1>
        
        <div class="instructions">
            <h3>使用说明：</h3>
            <ol>
                <li>点击下方的"生成图标"按钮</li>
                <li>右键点击生成的图标，选择"图片另存为"</li>
                <li>将图标保存到小程序的 images 目录下</li>
                <li>文件名要与 app.json 中配置的路径一致</li>
            </ol>
        </div>

        <div class="icon-grid">
            <!-- 首页图标 -->
            <div class="icon-item">
                <h3>首页图标</h3>
                <canvas id="home" width="81" height="81"></canvas>
                <canvas id="home-active" width="81" height="81"></canvas>
                <br>
                <button onclick="generateIcon('home', '#999999', '🏠')">生成未选中</button>
                <button onclick="generateIcon('home-active', '#ff6b35', '🏠')">生成选中</button>
                <br>
                <button onclick="downloadIcon('home')">下载 home.png</button>
                <button onclick="downloadIcon('home-active')">下载 home-active.png</button>
            </div>

            <!-- 分类图标 -->
            <div class="icon-item">
                <h3>分类图标</h3>
                <canvas id="category" width="81" height="81"></canvas>
                <canvas id="category-active" width="81" height="81"></canvas>
                <br>
                <button onclick="generateIcon('category', '#999999', '📋')">生成未选中</button>
                <button onclick="generateIcon('category-active', '#ff6b35', '📋')">生成选中</button>
                <br>
                <button onclick="downloadIcon('category')">下载 category.png</button>
                <button onclick="downloadIcon('category-active')">下载 category-active.png</button>
            </div>

            <!-- 购物车图标 -->
            <div class="icon-item">
                <h3>购物车图标</h3>
                <canvas id="cart" width="81" height="81"></canvas>
                <canvas id="cart-active" width="81" height="81"></canvas>
                <br>
                <button onclick="generateIcon('cart', '#999999', '🛒')">生成未选中</button>
                <button onclick="generateIcon('cart-active', '#ff6b35', '🛒')">生成选中</button>
                <br>
                <button onclick="downloadIcon('cart')">下载 cart.png</button>
                <button onclick="downloadIcon('cart-active')">下载 cart-active.png</button>
            </div>

            <!-- 个人中心图标 -->
            <div class="icon-item">
                <h3>个人中心图标</h3>
                <canvas id="profile" width="81" height="81"></canvas>
                <canvas id="profile-active" width="81" height="81"></canvas>
                <br>
                <button onclick="generateIcon('profile', '#999999', '👤')">生成未选中</button>
                <button onclick="generateIcon('profile-active', '#ff6b35', '👤')">生成选中</button>
                <br>
                <button onclick="downloadIcon('profile')">下载 profile.png</button>
                <button onclick="downloadIcon('profile-active')">下载 profile-active.png</button>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #fff3cd; border-radius: 8px;">
            <h3>💡 提示：</h3>
            <p>当前小程序已配置为使用文字图标（Emoji），无需额外的图片文件。如果您希望使用传统的PNG图标，可以使用此工具生成，然后修改 app.json 配置。</p>
        </div>
    </div>

    <script>
        function generateIcon(canvasId, color, emoji) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, 81, 81);
            
            // 设置背景（透明）
            ctx.fillStyle = 'transparent';
            ctx.fillRect(0, 0, 81, 81);
            
            // 绘制emoji图标
            ctx.font = '48px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillStyle = color;
            ctx.fillText(emoji, 40.5, 40.5);
        }

        function downloadIcon(canvasId) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = canvasId + '.png';
            link.href = canvas.toDataURL();
            link.click();
        }

        // 页面加载时生成所有图标
        window.onload = function() {
            generateIcon('home', '#999999', '🏠');
            generateIcon('home-active', '#ff6b35', '🏠');
            generateIcon('category', '#999999', '📋');
            generateIcon('category-active', '#ff6b35', '📋');
            generateIcon('cart', '#999999', '🛒');
            generateIcon('cart-active', '#ff6b35', '🛒');
            generateIcon('profile', '#999999', '👤');
            generateIcon('profile-active', '#ff6b35', '👤');
        };
    </script>
</body>
</html>
