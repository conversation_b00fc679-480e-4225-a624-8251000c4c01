@echo off
chcp 65001
echo ========================================
echo 🍽️  美食小程序后端启动脚本
echo ========================================

echo.
echo 📋 检查环境...

:: 检查Java环境
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java环境未安装或配置错误
    echo 请安装Java 8或更高版本
    pause
    exit /b 1
)
echo ✅ Java环境检查通过

:: 检查Maven环境
mvn -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Maven环境未安装或配置错误
    echo 请安装Maven并配置环境变量
    pause
    exit /b 1
)
echo ✅ Maven环境检查通过

echo.
echo 📦 安装依赖...
call mvn clean install -DskipTests

if %errorlevel% neq 0 (
    echo ❌ 依赖安装失败
    pause
    exit /b 1
)
echo ✅ 依赖安装完成

echo.
echo 📁 创建上传目录...
if not exist "uploads" mkdir uploads
if not exist "uploads\dishes" mkdir uploads\dishes
if not exist "uploads\banners" mkdir uploads\banners
if not exist "uploads\categories" mkdir uploads\categories
if not exist "uploads\avatars" mkdir uploads\avatars
if not exist "uploads\temp" mkdir uploads\temp
echo ✅ 上传目录创建完成

echo.
echo 🗄️  数据库初始化提示：
echo 请确保MySQL服务已启动
echo 请执行 src/main/resources/sql/init.sql 初始化数据库
echo.

echo 🚀 启动应用...
echo 应用将在 http://localhost:8080 启动
echo API文档: http://localhost:8080/api/doc.html
echo 图片访问: http://localhost:8080/api/images/
echo.

call mvn spring-boot:run

pause
