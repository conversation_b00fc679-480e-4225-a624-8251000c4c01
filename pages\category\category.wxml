<!--pages/category/category.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <input 
        placeholder="搜索菜品" 
        value="{{searchValue}}"
        bindinput="onSearchInput"
        bindconfirm="onSearchConfirm"
      />
    </view>
  </view>

  <!-- 分类导航 -->
  <view class="category-nav">
    <scroll-view class="category-scroll" scroll-x="{{true}}">
      <view class="category-list">
        <view 
          class="category-item {{currentCategoryId === null ? 'active' : ''}}"
          bindtap="onAllCategoryTap"
        >
          <text class="category-text">全部</text>
        </view>
        <view 
          class="category-item {{currentCategoryId === item.id ? 'active' : ''}}"
          wx:for="{{categories}}" 
          wx:key="id"
          bindtap="onCategoryTap"
          data-item="{{item}}"
        >
          <text class="category-text">{{item.name}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 菜品列表 -->
  <view class="dish-list">
    <view 
      class="dish-item" 
      wx:for="{{dishes}}" 
      wx:key="id"
      bindtap="onDishTap"
      data-item="{{item}}"
    >
      <image 
        class="dish-image" 
        src="{{item.image || '/images/dish-default.jpg'}}" 
        mode="aspectFill"
      />
      <view class="dish-info">
        <view class="dish-header">
          <text class="dish-name">{{item.name}}</text>
          <view class="dish-tags">
            <text class="tag hot" wx:if="{{item.is_hot}}">热</text>
            <text class="tag new" wx:if="{{item.is_new}}">新</text>
          </view>
        </view>
        <text class="dish-desc">{{item.description}}</text>
        <view class="dish-stats">
          <text class="sales-count">已售{{item.sales_count}}</text>
        </view>
        <view class="dish-footer">
          <text class="dish-price">¥{{item.price}}</text>
          <view 
            class="add-btn" 
            bindtap="onAddToCart"
            data-item="{{item}}"
          >
            <text class="add-icon">+</text>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && dishes.length === 0}}">
    <image class="empty-image" src="/images/empty-dish.png" mode="aspectFit"></image>
    <text class="empty-text">暂无菜品</text>
  </view>

  <!-- 加载更多 -->
  <view class="load-more" wx:if="{{!loading && dishes.length > 0 && hasMore}}">
    <text>上拉加载更多</text>
  </view>

  <!-- 没有更多 -->
  <view class="no-more" wx:if="{{!loading && dishes.length > 0 && !hasMore}}">
    <text>没有更多了</text>
  </view>
</view>
