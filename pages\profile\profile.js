// pages/profile/profile.js
const { get } = require('../../utils/request')
const { showConfirm } = require('../../utils/util')

Page({
  data: {
    userInfo: null,
    isLogin: false,
    orderStats: {
      pending: 0,
      confirmed: 0,
      completed: 0,
      cancelled: 0
    }
  },

  onLoad() {
    this.checkLoginStatus()
  },

  onShow() {
    this.checkLoginStatus()
    if (this.data.isLogin) {
      this.loadOrderStats()
    }
  },

  // 检查登录状态
  checkLoginStatus() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    const token = app.getToken()
    
    this.setData({
      userInfo,
      isLogin: !!(userInfo && token)
    })
  },

  // 加载订单统计
  async loadOrderStats() {
    try {
      const res = await get('/orders/stats')
      this.setData({
        orderStats: res.data
      })
    } catch (error) {
      console.error('加载订单统计失败:', error)
    }
  },

  // 去登录
  onLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 查看订单
  onViewOrders(e) {
    if (!this.data.isLogin) {
      this.onLogin()
      return
    }
    
    const { status } = e.currentTarget.dataset
    let url = '/pages/order-list/order-list'
    if (status) {
      url += `?status=${status}`
    }
    
    wx.navigateTo({
      url
    })
  },

  // 个人信息
  onProfile() {
    if (!this.data.isLogin) {
      this.onLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/user-info/user-info'
    })
  },

  // 收货地址
  onAddress() {
    if (!this.data.isLogin) {
      this.onLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/address/address'
    })
  },

  // 优惠券
  onCoupons() {
    if (!this.data.isLogin) {
      this.onLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/coupons/coupons'
    })
  },

  // 客服
  onCustomerService() {
    wx.makePhoneCall({
      phoneNumber: '************'
    })
  },

  // 关于我们
  onAbout() {
    wx.navigateTo({
      url: '/pages/about/about'
    })
  },

  // 设置
  onSettings() {
    if (!this.data.isLogin) {
      this.onLogin()
      return
    }
    
    wx.navigateTo({
      url: '/pages/settings/settings'
    })
  },

  // 退出登录
  async onLogout() {
    try {
      await showConfirm('确定要退出登录吗？')
      
      const app = getApp()
      app.clearAuth()
      
      this.setData({
        userInfo: null,
        isLogin: false,
        orderStats: {
          pending: 0,
          confirmed: 0,
          completed: 0,
          cancelled: 0
        }
      })
      
      wx.showToast({
        title: '已退出登录',
        icon: 'success'
      })
    } catch (error) {
      // 用户取消退出
    }
  },

  // 分享
  onShareAppMessage() {
    return {
      title: '在线点餐 - 美食就在指尖',
      path: '/pages/index/index',
      imageUrl: '/images/share.jpg'
    }
  }
})
