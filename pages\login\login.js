// pages/login/login.js
const { post } = require('../../utils/request')
const { validatePhone, showLoading, hideLoading } = require('../../utils/util')

Page({
  data: {
    loginType: 'phone', // phone: 手机号登录, wechat: 微信授权登录
    phone: '',
    code: '',
    countdown: 0,
    canGetCode: true,
    loading: false
  },

  onLoad(options) {
    // 检查是否已登录
    const app = getApp()
    const token = app.getToken()
    if (token) {
      this.redirectToTarget(options.redirect)
      return
    }
  },

  // 切换登录方式
  onSwitchLoginType(e) {
    const { type } = e.currentTarget.dataset
    this.setData({
      loginType: type,
      phone: '',
      code: '',
      countdown: 0,
      canGetCode: true
    })
  },

  // 手机号输入
  onPhoneInput(e) {
    this.setData({
      phone: e.detail.value
    })
  },

  // 验证码输入
  onCodeInput(e) {
    this.setData({
      code: e.detail.value
    })
  },

  // 获取验证码
  async onGetCode() {
    const { phone, canGetCode } = this.data
    
    if (!canGetCode) return
    
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!validatePhone(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }
    
    try {
      showLoading('发送中...')
      
      await post('/auth/send-code', { phone })
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
      // 开始倒计时
      this.startCountdown()
      
    } catch (error) {
      console.error('发送验证码失败:', error)
    } finally {
      hideLoading()
    }
  },

  // 开始倒计时
  startCountdown() {
    let countdown = 60
    this.setData({
      countdown,
      canGetCode: false
    })
    
    const timer = setInterval(() => {
      countdown--
      this.setData({
        countdown
      })
      
      if (countdown <= 0) {
        clearInterval(timer)
        this.setData({
          canGetCode: true,
          countdown: 0
        })
      }
    }, 1000)
  },

  // 手机号登录
  async onPhoneLogin() {
    const { phone, code } = this.data
    
    if (!phone) {
      wx.showToast({
        title: '请输入手机号',
        icon: 'none'
      })
      return
    }
    
    if (!validatePhone(phone)) {
      wx.showToast({
        title: '手机号格式不正确',
        icon: 'none'
      })
      return
    }
    
    if (!code) {
      wx.showToast({
        title: '请输入验证码',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({ loading: true })
      showLoading('登录中...')
      
      const res = await post('/auth/login', {
        phone,
        code
      })
      
      const app = getApp()
      app.setToken(res.data.token)
      app.setUserInfo(res.data.userInfo)
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        this.redirectToTarget()
      }, 1500)
      
    } catch (error) {
      console.error('登录失败:', error)
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  // 微信授权登录
  async onWechatLogin() {
    try {
      this.setData({ loading: true })
      showLoading('登录中...')
      
      // 获取用户信息
      const userProfile = await this.getUserProfile()
      
      // 获取登录凭证
      const loginRes = await this.wxLogin()
      
      // 发送到后端
      const res = await post('/auth/wechat-login', {
        code: loginRes.code,
        userInfo: userProfile.userInfo,
        signature: userProfile.signature,
        rawData: userProfile.rawData,
        encryptedData: userProfile.encryptedData,
        iv: userProfile.iv
      })
      
      const app = getApp()
      app.setToken(res.data.token)
      app.setUserInfo(res.data.userInfo)
      
      wx.showToast({
        title: '登录成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        this.redirectToTarget()
      }, 1500)
      
    } catch (error) {
      console.error('微信登录失败:', error)
      wx.showToast({
        title: '登录失败',
        icon: 'none'
      })
    } finally {
      this.setData({ loading: false })
      hideLoading()
    }
  },

  // 获取用户信息
  getUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })
  },

  // 微信登录
  wxLogin() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 跳转到目标页面
  redirectToTarget(redirect) {
    if (redirect) {
      wx.redirectTo({
        url: decodeURIComponent(redirect)
      })
    } else {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  }
})
