<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片资源设置 - 美食小程序后端</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            font-size: 2.5em;
        }
        
        .section {
            margin-bottom: 40px;
            padding: 25px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }
        
        .section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #ff6b35;
            display: inline-block;
        }
        
        .upload-area {
            border: 3px dashed #ff6b35;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            background: #e9ecef;
            border-color: #f7931e;
        }
        
        .upload-area.dragover {
            background: #fff3cd;
            border-color: #ffc107;
        }
        
        .upload-icon {
            font-size: 48px;
            color: #ff6b35;
            margin-bottom: 15px;
        }
        
        .upload-text {
            font-size: 18px;
            color: #6c757d;
            margin-bottom: 15px;
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
        }
        
        .file-list {
            margin-top: 20px;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .file-info {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .file-preview {
            width: 50px;
            height: 50px;
            border-radius: 5px;
            object-fit: cover;
        }
        
        .file-name {
            font-weight: 500;
            color: #2c3e50;
        }
        
        .file-size {
            color: #6c757d;
            font-size: 14px;
        }
        
        .file-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn {
            padding: 5px 15px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
        }
        
        .btn-upload {
            background: #28a745;
            color: white;
        }
        
        .btn-delete {
            background: #dc3545;
            color: white;
        }
        
        .btn:hover {
            transform: scale(1.05);
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
            margin-top: 10px;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e3f2fd;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #2196f3;
        }
        
        .instructions h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }
        
        .instructions ol {
            margin-left: 20px;
        }
        
        .instructions li {
            margin: 8px 0;
            color: #424242;
        }
        
        .api-info {
            background: #f3e5f5;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
            border-left: 4px solid #9c27b0;
        }
        
        .api-info h3 {
            color: #7b1fa2;
            margin-bottom: 15px;
        }
        
        .api-endpoint {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ 图片资源设置</h1>
        
        <div class="instructions">
            <h3>📋 使用说明</h3>
            <ol>
                <li>确保后端服务已启动（http://localhost:8080）</li>
                <li>选择对应的图片类型进行上传</li>
                <li>支持拖拽上传和点击选择文件</li>
                <li>上传成功后图片将自动保存到服务器</li>
                <li>图片URL会自动更新到数据库中</li>
            </ol>
        </div>

        <!-- 菜品图片上传 -->
        <div class="section">
            <h2>🍽️ 菜品图片</h2>
            <div class="upload-area" id="dishUpload">
                <div class="upload-icon">📸</div>
                <div class="upload-text">拖拽菜品图片到此处或点击上传</div>
                <button class="upload-btn" onclick="document.getElementById('dishFiles').click()">选择文件</button>
                <input type="file" id="dishFiles" class="file-input" multiple accept="image/*">
            </div>
            <div class="file-list" id="dishFileList"></div>
        </div>

        <!-- 轮播图上传 -->
        <div class="section">
            <h2>🎨 轮播图</h2>
            <div class="upload-area" id="bannerUpload">
                <div class="upload-icon">🖼️</div>
                <div class="upload-text">拖拽轮播图到此处或点击上传</div>
                <button class="upload-btn" onclick="document.getElementById('bannerFiles').click()">选择文件</button>
                <input type="file" id="bannerFiles" class="file-input" multiple accept="image/*">
            </div>
            <div class="file-list" id="bannerFileList"></div>
        </div>

        <!-- 分类图标上传 -->
        <div class="section">
            <h2>📂 分类图标</h2>
            <div class="upload-area" id="categoryUpload">
                <div class="upload-icon">🏷️</div>
                <div class="upload-text">拖拽分类图标到此处或点击上传</div>
                <button class="upload-btn" onclick="document.getElementById('categoryFiles').click()">选择文件</button>
                <input type="file" id="categoryFiles" class="file-input" multiple accept="image/*">
            </div>
            <div class="file-list" id="categoryFileList"></div>
        </div>

        <div class="api-info">
            <h3>🔗 API 接口信息</h3>
            <p><strong>上传接口：</strong></p>
            <div class="api-endpoint">POST http://localhost:8080/api/file/upload</div>
            <p><strong>图片访问：</strong></p>
            <div class="api-endpoint">GET http://localhost:8080/api/images/{path}</div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        // 初始化上传功能
        function initUpload(uploadAreaId, fileInputId, fileListId, type) {
            const uploadArea = document.getElementById(uploadAreaId);
            const fileInput = document.getElementById(fileInputId);
            const fileList = document.getElementById(fileListId);
            
            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });
            
            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });
            
            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                const files = e.dataTransfer.files;
                handleFiles(files, fileList, type);
            });
            
            // 文件选择
            fileInput.addEventListener('change', (e) => {
                const files = e.target.files;
                handleFiles(files, fileList, type);
            });
        }
        
        // 处理文件
        function handleFiles(files, fileListContainer, type) {
            Array.from(files).forEach(file => {
                if (file.type.startsWith('image/')) {
                    addFileToList(file, fileListContainer, type);
                }
            });
        }
        
        // 添加文件到列表
        function addFileToList(file, container, type) {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            
            const fileInfo = document.createElement('div');
            fileInfo.className = 'file-info';
            
            const preview = document.createElement('img');
            preview.className = 'file-preview';
            preview.src = URL.createObjectURL(file);
            
            const details = document.createElement('div');
            details.innerHTML = `
                <div class="file-name">${file.name}</div>
                <div class="file-size">${formatFileSize(file.size)}</div>
            `;
            
            fileInfo.appendChild(preview);
            fileInfo.appendChild(details);
            
            const actions = document.createElement('div');
            actions.className = 'file-actions';
            
            const uploadBtn = document.createElement('button');
            uploadBtn.className = 'btn btn-upload';
            uploadBtn.textContent = '上传';
            uploadBtn.onclick = () => uploadFile(file, type, fileItem);
            
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-delete';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = () => fileItem.remove();
            
            actions.appendChild(uploadBtn);
            actions.appendChild(deleteBtn);
            
            fileItem.appendChild(fileInfo);
            fileItem.appendChild(actions);
            
            container.appendChild(fileItem);
        }
        
        // 上传文件
        async function uploadFile(file, type, fileItem) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('type', type);
            
            // 添加进度条
            const progressBar = document.createElement('div');
            progressBar.className = 'progress-bar';
            progressBar.innerHTML = '<div class="progress-fill"></div>';
            fileItem.appendChild(progressBar);
            
            try {
                const response = await fetch(`${API_BASE}/file/upload`, {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.code === 200) {
                    showStatus(fileItem, 'success', `上传成功！URL: ${result.data.url}`);
                    progressBar.querySelector('.progress-fill').style.width = '100%';
                } else {
                    showStatus(fileItem, 'error', `上传失败: ${result.message}`);
                }
            } catch (error) {
                showStatus(fileItem, 'error', `上传失败: ${error.message}`);
            }
        }
        
        // 显示状态
        function showStatus(container, type, message) {
            const status = document.createElement('div');
            status.className = `status ${type}`;
            status.textContent = message;
            container.appendChild(status);
        }
        
        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        // 初始化所有上传区域
        initUpload('dishUpload', 'dishFiles', 'dishFileList', 'dishes');
        initUpload('bannerUpload', 'bannerFiles', 'bannerFileList', 'banners');
        initUpload('categoryUpload', 'categoryFiles', 'categoryFileList', 'categories');
        
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/file/config`);
                if (response.ok) {
                    console.log('✅ 服务器连接正常');
                } else {
                    console.warn('⚠️ 服务器响应异常');
                }
            } catch (error) {
                console.error('❌ 无法连接到服务器，请确保后端服务已启动');
            }
        }
        
        // 页面加载时检查服务器状态
        checkServerStatus();
    </script>
</body>
</html>
