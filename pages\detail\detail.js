// pages/detail/detail.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, getImageUrl, formatPrice } = require('../../utils/util')

Page({
  data: {
    dish: null,
    loading: true,
    quantity: 1,
    relatedDishes: []
  },

  onLoad(options) {
    const { id } = options
    if (id) {
      this.loadDishDetail(id)
      this.loadRelatedDishes(id)
    }
  },

  onShow() {
    this.updateCartCount()
  },

  // 加载菜品详情
  async loadDishDetail(id) {
    try {
      this.setData({ loading: true })
      showLoading('加载中...')
      
      const res = await get(`/dishes/${id}`)
      
      this.setData({
        dish: res.data,
        loading: false
      })
      
      // 设置页面标题
      wx.setNavigationBarTitle({
        title: res.data.name
      })
    } catch (error) {
      console.error('加载菜品详情失败:', error)
      this.setData({ loading: false })
      
      wx.showToast({
        title: '加载失败',
        icon: 'none'
      })
      
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      hideLoading()
    }
  },

  // 加载相关菜品
  async loadRelatedDishes(currentId) {
    try {
      const { dish } = this.data
      if (!dish) return
      
      const res = await get('/dishes', {
        category_id: dish.category_id,
        limit: 6
      })
      
      // 过滤掉当前菜品
      const relatedDishes = res.data.list.filter(item => item.id != currentId)
      
      this.setData({
        relatedDishes: relatedDishes.slice(0, 4)
      })
    } catch (error) {
      console.error('加载相关菜品失败:', error)
    }
  },

  // 预览图片
  onImageTap() {
    const { dish } = this.data
    if (dish && dish.image) {
      wx.previewImage({
        current: getImageUrl(dish.image),
        urls: [getImageUrl(dish.image)]
      })
    }
  },

  // 减少数量
  onDecreaseQuantity() {
    const { quantity } = this.data
    if (quantity > 1) {
      this.setData({
        quantity: quantity - 1
      })
    }
  },

  // 增加数量
  onIncreaseQuantity() {
    const { quantity } = this.data
    this.setData({
      quantity: quantity + 1
    })
  },

  // 输入数量
  onQuantityInput(e) {
    let quantity = parseInt(e.detail.value) || 1
    if (quantity < 1) quantity = 1
    if (quantity > 99) quantity = 99
    
    this.setData({ quantity })
  },

  // 添加到购物车
  onAddToCart() {
    const { dish, quantity } = this.data
    
    if (!dish) {
      wx.showToast({
        title: '菜品信息加载中',
        icon: 'none'
      })
      return
    }
    
    const app = getApp()
    app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity
    })
    
    wx.showToast({
      title: `已添加${quantity}份到购物车`,
      icon: 'success'
    })
    
    this.updateCartCount()
    
    // 重置数量
    this.setData({ quantity: 1 })
  },

  // 立即购买
  onBuyNow() {
    const { dish, quantity } = this.data
    
    if (!dish) {
      wx.showToast({
        title: '菜品信息加载中',
        icon: 'none'
      })
      return
    }
    
    // 清空购物车并添加当前商品
    const app = getApp()
    app.clearCart()
    app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity
    })
    
    // 跳转到订单页面
    const totalPrice = formatPrice(dish.price * quantity)
    wx.navigateTo({
      url: `/pages/order/order?from=detail&totalPrice=${totalPrice}`
    })
  },

  // 点击相关菜品
  onRelatedDishTap(e) {
    const { item } = e.currentTarget.dataset
    wx.redirectTo({
      url: `/pages/detail/detail?id=${item.id}`
    })
  },

  // 查看分类
  onCategoryTap() {
    const { dish } = this.data
    if (dish && dish.category_id) {
      wx.navigateTo({
        url: `/pages/category/category?categoryId=${dish.category_id}`
      })
    }
  },

  // 分享
  onShareAppMessage() {
    const { dish } = this.data
    return {
      title: dish ? `${dish.name} - 在线点餐` : '美味菜品 - 在线点餐',
      path: `/pages/detail/detail?id=${dish?.id}`,
      imageUrl: dish ? getImageUrl(dish.image) : '/images/share.jpg'
    }
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    
    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 获取图片URL
  getImageUrl(path) {
    return getImageUrl(path)
  },

  // 格式化价格
  formatPrice(price) {
    return formatPrice(price)
  }
})
