# 🍽️ 美食小程序后端服务

基于Spring Boot的微信小程序美食后端服务，提供完整的菜品管理、图片上传、数据接口等功能。

## 📋 功能特性

### 🎯 核心功能
- **菜品管理**: 菜品CRUD、分类管理、标签系统
- **图片服务**: 文件上传、图片压缩、缩略图生成
- **数据接口**: RESTful API、分页查询、搜索功能
- **轮播图**: 首页轮播图管理
- **统计分析**: 销量统计、评分统计

### 🛠️ 技术栈
- **框架**: Spring Boot 2.7.14
- **数据库**: MySQL 8.0 + MyBatis Plus
- **缓存**: Redis
- **文件处理**: Thumbnailator (图片处理)
- **工具类**: Hutool
- **安全**: Spring Security + JWT

## 🚀 快速开始

### 环境要求
- Java 8+
- Maven 3.6+
- MySQL 8.0+
- Redis (可选)

### 1. 克隆项目
```bash
git clone <repository-url>
cd backend
```

### 2. 数据库配置
1. 创建MySQL数据库
2. 执行初始化脚本：`src/main/resources/sql/init.sql`
3. 修改配置文件：`src/main/resources/application.yml`

```yaml
spring:
  datasource:
    url: ********************************************
    username: your_username
    password: your_password
```

### 3. 启动服务

#### Windows
```bash
# 双击运行
start.bat

# 或者命令行
mvn spring-boot:run
```

#### Linux/Mac
```bash
chmod +x start.sh
./start.sh

# 或者
mvn spring-boot:run
```

### 4. 验证启动
- 应用地址: http://localhost:8080
- API文档: http://localhost:8080/api/doc.html
- 图片访问: http://localhost:8080/api/images/

## 📁 项目结构

```
backend/
├── src/main/java/com/foodapp/
│   ├── controller/          # 控制器层
│   │   ├── DishController.java
│   │   └── FileController.java
│   ├── service/            # 服务层
│   │   ├── DishService.java
│   │   └── FileService.java
│   ├── entity/             # 实体类
│   │   ├── Dish.java
│   │   └── DishImage.java
│   ├── mapper/             # 数据访问层
│   ├── config/             # 配置类
│   │   └── WebConfig.java
│   └── common/             # 公共类
│       └── Result.java
├── src/main/resources/
│   ├── application.yml     # 配置文件
│   └── sql/init.sql       # 数据库初始化脚本
├── uploads/               # 文件上传目录
├── start.bat             # Windows启动脚本
├── image-setup.html      # 图片上传工具
└── README.md
```

## 🖼️ 图片管理

### 上传图片
1. 启动后端服务
2. 打开 `image-setup.html` 文件
3. 选择对应类型的图片进行上传
4. 支持拖拽上传和批量上传

### 图片类型
- **菜品图片**: `/uploads/dishes/`
- **轮播图**: `/uploads/banners/`
- **分类图标**: `/uploads/categories/`
- **头像图片**: `/uploads/avatars/`

### 图片处理功能
- 自动压缩
- 缩略图生成
- 格式转换
- 尺寸调整

## 🔌 API 接口

### 文件上传接口

#### 单文件上传
```http
POST /api/file/upload
Content-Type: multipart/form-data

Parameters:
- file: 文件
- type: 文件类型 (dishes/banners/categories)
```

#### 批量上传
```http
POST /api/file/upload/batch
Content-Type: multipart/form-data

Parameters:
- files: 文件数组
- type: 文件类型
```

#### 图片压缩
```http
POST /api/file/compress
Content-Type: multipart/form-data

Parameters:
- file: 图片文件
- quality: 压缩质量 (0-1)
- width: 宽度 (可选)
- height: 高度 (可选)
```

### 菜品接口

#### 分页查询菜品
```http
GET /api/dish/page?current=1&size=10&categoryId=1&keyword=宫保
```

#### 获取菜品详情
```http
GET /api/dish/{id}
```

#### 获取热门菜品
```http
GET /api/dish/hot?limit=10
```

#### 获取新品推荐
```http
GET /api/dish/new?limit=10
```

#### 搜索菜品
```http
GET /api/dish/search?keyword=鸡肉&limit=20
```

## ⚙️ 配置说明

### 文件上传配置
```yaml
food-app:
  upload:
    path: /uploads/              # 上传路径
    access-path: /images/**      # 访问路径
    allowed-types:               # 允许的文件类型
      - jpg
      - jpeg
      - png
      - gif
      - webp
    max-size: 10                 # 最大文件大小(MB)
```

### 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************
    username: root
    password: 123456
```

### Redis配置 (可选)
```yaml
spring:
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
```

## 🔧 开发指南

### 添加新的菜品分类
1. 在数据库 `category` 表中添加记录
2. 上传对应的分类图标
3. 重启服务或刷新缓存

### 添加新的菜品
1. 准备菜品图片
2. 通过图片上传接口上传图片
3. 在数据库 `dish` 表中添加菜品记录
4. 关联图片URL

### 自定义图片处理
修改 `FileServiceImpl.java` 中的图片处理逻辑：
```java
// 自定义压缩质量
Thumbnails.of(file.getInputStream())
    .outputQuality(0.8f)
    .size(800, 600)
    .toFile(targetFile);
```

## 🐛 常见问题

### 1. 启动失败
- 检查Java版本是否为8+
- 检查MySQL服务是否启动
- 检查端口8080是否被占用

### 2. 图片上传失败
- 检查上传目录权限
- 检查文件大小是否超过限制
- 检查文件格式是否支持

### 3. 数据库连接失败
- 检查MySQL服务状态
- 检查数据库用户名密码
- 检查数据库是否存在

### 4. 跨域问题
已在 `WebConfig.java` 中配置跨域支持，如需修改：
```java
@Override
public void addCorsMappings(CorsRegistry registry) {
    registry.addMapping("/**")
            .allowedOriginPatterns("*")
            .allowedMethods("GET", "POST", "PUT", "DELETE", "OPTIONS");
}
```

## 📝 更新日志

### v1.0.0 (2024-01-01)
- ✨ 初始版本发布
- 🎯 基础菜品管理功能
- 🖼️ 图片上传和处理功能
- 📱 微信小程序API接口
- 🗄️ 数据库设计和初始化

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 联系我们

- 项目地址: [GitHub Repository]
- 问题反馈: [Issues]
- 邮箱: <EMAIL>

---

🍽️ **美食小程序后端** - 让美食触手可及！
