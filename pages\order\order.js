// pages/order/order.js
const { post } = require('../../utils/request')
const { showLoading, hideLoading, formatPrice, validatePhone } = require('../../utils/util')

Page({
  data: {
    orderItems: [],
    totalPrice: 0,
    totalCount: 0,
    contactName: '',
    contactPhone: '',
    deliveryAddress: '',
    remark: '',
    submitting: false
  },

  onLoad(options) {
    const { from, totalPrice } = options
    
    // 获取购物车数据
    const app = getApp()
    const cartData = app.getCartData()
    
    if (cartData.cart.length === 0) {
      wx.showToast({
        title: '购物车为空',
        icon: 'none'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }
    
    // 计算订单信息
    const orderItems = cartData.cart.map(item => ({
      ...item,
      subtotal: item.price * item.quantity
    }))
    
    const totalCount = orderItems.reduce((total, item) => total + item.quantity, 0)
    const calculatedTotal = orderItems.reduce((total, item) => total + item.subtotal, 0)
    
    this.setData({
      orderItems,
      totalPrice: formatPrice(calculatedTotal),
      totalCount
    })
    
    // 获取用户信息
    this.loadUserInfo()
  },

  // 加载用户信息
  loadUserInfo() {
    const app = getApp()
    const userInfo = app.getUserInfo()
    
    if (userInfo) {
      this.setData({
        contactName: userInfo.nickname || '',
        contactPhone: userInfo.phone || ''
      })
    }
  },

  // 联系人姓名输入
  onContactNameInput(e) {
    this.setData({
      contactName: e.detail.value
    })
  },

  // 联系人电话输入
  onContactPhoneInput(e) {
    this.setData({
      contactPhone: e.detail.value
    })
  },

  // 配送地址输入
  onDeliveryAddressInput(e) {
    this.setData({
      deliveryAddress: e.detail.value
    })
  },

  // 备注输入
  onRemarkInput(e) {
    this.setData({
      remark: e.detail.value
    })
  },

  // 选择地址
  onChooseAddress() {
    wx.chooseAddress({
      success: (res) => {
        const address = `${res.provinceName}${res.cityName}${res.countyName}${res.detailInfo}`
        this.setData({
          deliveryAddress: address,
          contactName: res.userName,
          contactPhone: res.telNumber
        })
      },
      fail: (err) => {
        if (err.errMsg.includes('deny')) {
          wx.showModal({
            title: '提示',
            content: '需要获取您的地址信息，请在设置中开启地址权限',
            showCancel: false
          })
        }
      }
    })
  },

  // 提交订单
  async onSubmitOrder() {
    if (this.data.submitting) return
    
    const {
      orderItems,
      totalPrice,
      contactName,
      contactPhone,
      deliveryAddress,
      remark
    } = this.data
    
    // 验证表单
    if (!contactName.trim()) {
      wx.showToast({
        title: '请输入联系人姓名',
        icon: 'none'
      })
      return
    }
    
    if (!contactPhone.trim()) {
      wx.showToast({
        title: '请输入联系人电话',
        icon: 'none'
      })
      return
    }
    
    if (!validatePhone(contactPhone)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'none'
      })
      return
    }
    
    if (!deliveryAddress.trim()) {
      wx.showToast({
        title: '请输入配送地址',
        icon: 'none'
      })
      return
    }
    
    try {
      this.setData({ submitting: true })
      showLoading('提交订单中...')
      
      // 检查登录状态
      const app = getApp()
      const token = app.getToken()
      
      if (!token) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        })
        
        setTimeout(() => {
          wx.navigateTo({
            url: '/pages/login/login'
          })
        }, 1500)
        return
      }
      
      // 准备订单数据
      const orderData = {
        items: orderItems.map(item => ({
          dish_id: item.id,
          quantity: item.quantity
        })),
        total_amount: parseFloat(totalPrice),
        contact_name: contactName.trim(),
        contact_phone: contactPhone.trim(),
        delivery_address: deliveryAddress.trim(),
        remark: remark.trim() || null
      }
      
      // 提交订单
      const res = await post('/orders', orderData)
      
      // 清空购物车
      app.clearCart()
      
      wx.showToast({
        title: '订单提交成功',
        icon: 'success'
      })
      
      // 跳转到订单详情页
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/order-detail/order-detail?id=${res.data.order_id}`
        })
      }, 1500)
      
    } catch (error) {
      console.error('提交订单失败:', error)
      wx.showToast({
        title: error.message || '提交订单失败',
        icon: 'none'
      })
    } finally {
      this.setData({ submitting: false })
      hideLoading()
    }
  },

  // 返回购物车
  onBackToCart() {
    wx.navigateBack()
  },

  // 获取图片URL
  getImageUrl(path) {
    const { getImageUrl } = require('../../utils/util')
    return getImageUrl(path)
  }
})
