<!--pages/index/index.wxml-->
<view class="container">
  <!-- 智能搜索组件 -->
  <smart-search
    placeholder="搜索美味菜品..."
    hot-keywords="{{hotKeywords}}"
    bind:search="onSearch"
  />

  <!-- 轮播图 -->
  <view class="banner-section" wx:if="{{banners.length > 0}}">
    <swiper
      class="banner-swiper"
      indicator-dots="{{true}}"
      indicator-color="rgba(255, 255, 255, 0.5)"
      indicator-active-color="#ff6b35"
      autoplay="{{true}}"
      interval="4000"
      duration="800"
      circular="{{true}}"
      easing-function="easeInOutCubic"
    >
      <swiper-item
        wx:for="{{banners}}"
        wx:key="id"
        bindtap="onBannerTap"
        data-item="{{item}}"
      >
        <view class="banner-item">
          <image
            class="banner-image"
            src="{{item.image || '/images/placeholder.jpg'}}"
            mode="aspectFill"
            lazy-load="{{true}}"
          />
          <view class="banner-overlay">
            <view class="banner-content">
              <text class="banner-title">{{item.title}}</text>
              <text class="banner-subtitle">美味就在指尖</text>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 分类导航 -->
  <view class="category-section" wx:if="{{categories.length > 0}}">
    <view class="section-title">菜品分类</view>
    <view class="category-grid">
      <view
        class="category-item"
        wx:for="{{categories}}"
        wx:key="id"
        bindtap="onCategoryTap"
        data-item="{{item}}"
      >
        <image
          class="category-icon"
          src="{{item.icon || '/images/category-default.jpg'}}"
          mode="aspectFill"
        />
        <text class="category-name">{{item.name}}</text>
      </view>
    </view>
  </view>

  <!-- 热门菜品 -->
  <view class="hot-section" wx:if="{{hotDishes.length > 0}}">
    <view class="section-header">
      <view class="section-title-wrapper">
        <text class="section-icon">🔥</text>
        <text class="section-title">热门菜品</text>
        <text class="section-subtitle">大家都在点</text>
      </view>
      <text class="section-more" bindtap="onMoreHotDishes">更多 ></text>
    </view>
    <scroll-view class="dish-scroll" scroll-x="{{true}}" show-scrollbar="{{false}}">
      <view class="dish-list">
        <dish-card
          wx:for="{{hotDishes}}"
          wx:key="id"
          dish="{{item}}"
          mode="featured"
          bind:dishTap="onDishTap"
          bind:addToCart="onAddToCart"
          bind:toggleLike="onToggleLike"
        />
      </view>
    </scroll-view>
  </view>

  <!-- 新品推荐 -->
  <view class="new-section" wx:if="{{newDishes.length > 0}}">
    <view class="section-header">
      <view class="section-title-wrapper">
        <text class="section-icon">✨</text>
        <text class="section-title">新品推荐</text>
        <text class="section-subtitle">尝鲜必选</text>
      </view>
      <text class="section-more" bindtap="onMoreNewDishes">更多 ></text>
    </view>
    <view class="new-list">
      <dish-card
        wx:for="{{newDishes}}"
        wx:key="id"
        dish="{{item}}"
        mode="normal"
        bind:dishTap="onDishTap"
        bind:addToCart="onAddToCart"
        bind:toggleLike="onToggleLike"
      />
    </view>
  </view>

  <!-- 悬浮购物车 -->
  <floating-cart
    cart-count="{{cartCount}}"
    total-price="{{totalPrice}}"
    show="{{cartCount > 0}}"
    bind:cartTap="onFloatingCartTap"
    bind:checkoutTap="onFloatingCheckoutTap"
  />

  <!-- 加载状态 -->
  <view class="loading" wx:if="{{loading}}">
    <text>加载中...</text>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{!loading && categories.length === 0}}">
    <text>暂无数据</text>
  </view>
</view>
