/* pages/detail/detail.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 120rpx;
}

/* 菜品图片 */
.image-section {
  position: relative;
  height: 500rpx;
  background-color: white;
}

.dish-image {
  width: 100%;
  height: 100%;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(transparent 60%, rgba(0, 0, 0, 0.3));
  display: flex;
  align-items: flex-end;
  padding: 30rpx;
}

.tags {
  display: flex;
  gap: 15rpx;
}

.tag {
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: white;
  font-weight: bold;
}

.tag.hot {
  background-color: #ff4757;
}

.tag.new {
  background-color: #2ed573;
}

/* 菜品信息 */
.info-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.dish-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20rpx;
}

.dish-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  flex: 1;
  margin-right: 20rpx;
}

.dish-price {
  font-size: 42rpx;
  color: #ff6b35;
  font-weight: bold;
}

.dish-meta {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  gap: 30rpx;
}

.category {
  font-size: 26rpx;
  color: #ff6b35;
  background-color: rgba(255, 107, 53, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.sales {
  font-size: 24rpx;
  color: #999;
}

.dish-description {
  border-top: 1rpx solid #eee;
  padding-top: 30rpx;
}

.desc-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 15rpx;
}

.desc-content {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
}

/* 数量选择 */
.quantity-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.quantity-label {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
}

.quantity-control {
  display: flex;
  align-items: center;
}

.quantity-btn {
  width: 60rpx;
  height: 60rpx;
  border: 1rpx solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
}

.quantity-btn.decrease {
  border-radius: 12rpx 0 0 12rpx;
  color: #666;
}

.quantity-btn.increase {
  border-radius: 0 12rpx 12rpx 0;
  background-color: #ff6b35;
  border-color: #ff6b35;
  color: white;
}

.quantity-btn.disabled {
  background-color: #f5f5f5;
  color: #ccc;
  border-color: #eee;
}

.quantity-input {
  width: 80rpx;
  height: 60rpx;
  border-top: 1rpx solid #ddd;
  border-bottom: 1rpx solid #ddd;
  text-align: center;
  font-size: 28rpx;
  background-color: #f8f9fa;
}

/* 相关推荐 */
.related-section {
  background-color: white;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 30rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.related-scroll {
  white-space: nowrap;
}

.related-list {
  display: flex;
}

.related-item {
  display: inline-block;
  width: 200rpx;
  margin-right: 20rpx;
  text-align: center;
}

.related-item:last-child {
  margin-right: 0;
}

.related-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  margin-bottom: 15rpx;
}

.related-name {
  font-size: 24rpx;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.related-price {
  font-size: 26rpx;
  color: #ff6b35;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.total-info {
  flex: 1;
}

.total-label {
  font-size: 24rpx;
  color: #666;
}

.total-price {
  font-size: 36rpx;
  color: #ff6b35;
  font-weight: bold;
  margin-left: 10rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.cart-btn, .buy-btn {
  padding: 20rpx 40rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  border: none;
}

.cart-btn {
  background-color: rgba(255, 107, 53, 0.1);
  color: #ff6b35;
}

.buy-btn {
  background-color: #ff6b35;
  color: white;
}

/* 状态页面 */
.loading-container, .error-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading, .error {
  text-align: center;
  color: #999;
  font-size: 28rpx;
}
