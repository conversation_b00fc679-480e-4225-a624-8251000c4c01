// components/dish-card/dish-card.js
Component({
  properties: {
    dish: {
      type: Object,
      value: {}
    },
    mode: {
      type: String,
      value: 'normal' // normal, compact, featured
    }
  },

  data: {
    addAnimation: {},
    likeAnimation: {},
    isLiked: false
  },

  methods: {
    // 点击菜品
    onDishTap() {
      this.triggerEvent('dishTap', { dish: this.data.dish })
    },

    // 添加到购物车
    onAddToCart(e) {
      e.stopPropagation()
      
      // 播放添加动画
      this.playAddAnimation()
      
      this.triggerEvent('addToCart', { dish: this.data.dish })
    },

    // 点赞/取消点赞
    onToggleLike(e) {
      e.stopPropagation()
      
      const isLiked = !this.data.isLiked
      this.setData({ isLiked })
      
      // 播放点赞动画
      this.playLikeAnimation()
      
      this.triggerEvent('toggleLike', { 
        dish: this.data.dish, 
        isLiked 
      })
    },

    // 播放添加动画
    playAddAnimation() {
      const animation = wx.createAnimation({
        duration: 400,
        timingFunction: 'ease-out'
      })

      // 缩放动画
      animation.scale(1.2).step({ duration: 150 })
      animation.scale(1).step({ duration: 150 })
      animation.rotate(360).step({ duration: 100 })

      this.setData({
        addAnimation: animation.export()
      })

      // 重置动画
      setTimeout(() => {
        this.setData({
          addAnimation: {}
        })
      }, 400)
    },

    // 播放点赞动画
    playLikeAnimation() {
      const animation = wx.createAnimation({
        duration: 600,
        timingFunction: 'ease-out'
      })

      if (this.data.isLiked) {
        // 点赞动画
        animation.scale(1.3).step({ duration: 200 })
        animation.scale(1).step({ duration: 200 })
        animation.rotate(15).step({ duration: 100 })
        animation.rotate(-15).step({ duration: 100 })
        animation.rotate(0).step({ duration: 100 })
      } else {
        // 取消点赞动画
        animation.scale(0.8).step({ duration: 200 })
        animation.scale(1).step({ duration: 200 })
      }

      this.setData({
        likeAnimation: animation.export()
      })

      // 重置动画
      setTimeout(() => {
        this.setData({
          likeAnimation: {}
        })
      }, 600)
    },

    // 获取图片URL
    getImageUrl(path) {
      if (!path) return '/images/dish-default.jpg'
      if (path.startsWith('http')) return path
      return path
    },

    // 格式化价格
    formatPrice(price) {
      return parseFloat(price).toFixed(2)
    }
  }
})
