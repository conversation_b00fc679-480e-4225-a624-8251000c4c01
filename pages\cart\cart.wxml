<!--pages/cart/cart.wxml-->
<view class="container">
  <!-- 购物车为空 -->
  <view class="empty-cart" wx:if="{{isEmpty}}">
    <image class="empty-image" src="/images/empty-cart.png" mode="aspectFit"></image>
    <text class="empty-text">购物车空空如也</text>
    <button class="continue-btn" bindtap="onContinueShopping">去逛逛</button>
  </view>

  <!-- 购物车有商品 -->
  <view class="cart-content" wx:else>
    <!-- 头部操作栏 -->
    <view class="cart-header">
      <view class="select-all" bindtap="onSelectAll">
        <view class="checkbox {{selectedAll ? 'checked' : ''}}">
          <text class="check-icon" wx:if="{{selectedAll}}">✓</text>
        </view>
        <text class="select-text">全选</text>
      </view>
      <text class="clear-btn" bindtap="onClearCart">清空购物车</text>
    </view>

    <!-- 商品列表 -->
    <view class="cart-list">
      <view 
        class="cart-item" 
        wx:for="{{cartItems}}" 
        wx:key="id"
        wx:for-index="index"
      >
        <!-- 选择框 -->
        <view class="item-select" bindtap="onSelectItem" data-index="{{index}}">
          <view class="checkbox {{item.selected ? 'checked' : ''}}">
            <text class="check-icon" wx:if="{{item.selected}}">✓</text>
          </view>
        </view>

        <!-- 商品信息 -->
        <view class="item-info">
          <image 
            class="item-image" 
            src="{{item.image || '/images/dish-default.jpg'}}" 
            mode="aspectFill"
          ></image>
          <view class="item-details">
            <text class="item-name">{{item.name}}</text>
            <text class="item-price">¥{{item.price}}</text>
          </view>
        </view>

        <!-- 数量控制 -->
        <view class="quantity-control">
          <view 
            class="quantity-btn decrease {{item.quantity <= 1 ? 'disabled' : ''}}" 
            bindtap="onDecreaseQuantity"
            data-index="{{index}}"
          >
            <text>-</text>
          </view>
          <text class="quantity-text">{{item.quantity}}</text>
          <view 
            class="quantity-btn increase" 
            bindtap="onIncreaseQuantity"
            data-index="{{index}}"
          >
            <text>+</text>
          </view>
        </view>

        <!-- 删除按钮 -->
        <view class="item-delete" bindtap="onDeleteItem" data-index="{{index}}">
          <text class="delete-icon">🗑️</text>
        </view>
      </view>
    </view>

    <!-- 底部结算栏 -->
    <view class="cart-footer">
      <view class="footer-info">
        <text class="total-count">共{{totalCount}}件商品</text>
        <view class="total-price">
          <text class="price-label">合计：</text>
          <text class="price-value">¥{{totalPrice}}</text>
        </view>
      </view>
      <button class="checkout-btn" bindtap="onCheckout">
        去结算
      </button>
    </view>
  </view>
</view>
