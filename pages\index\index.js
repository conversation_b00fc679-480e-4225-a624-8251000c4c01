// pages/index/index.js
const { get } = require('../../utils/request')
const { showLoading, hideLoading, getImageUrl } = require('../../utils/util')

Page({
  data: {
    banners: [], // 轮播图
    categories: [], // 分类
    hotDishes: [], // 热门菜品
    newDishes: [], // 新品推荐
    loading: true,
    searchValue: '',
    hotKeywords: ['川菜', '粤菜', '湘菜', '素食', '甜品', '汤类'],
    cartCount: 0,
    totalPrice: 0
  },

  onLoad() {
    this.loadData()
  },

  onShow() {
    // 更新购物车数量
    this.updateCartCount()
  },

  onPullDownRefresh() {
    this.loadData().then(() => {
      wx.stopPullDownRefresh()
    })
  },

  // 加载数据
  async loadData() {
    try {
      showLoading('加载中...')
      console.log('开始加载数据...')
      console.log('API基础地址:', getApp().globalData.baseUrl)

      // 先测试简单的健康检查
      console.log('测试健康检查接口...')
      const healthRes = await this.testRequest('/health')
      console.log('健康检查结果:', healthRes)

      // 逐个请求接口，便于调试
      console.log('请求轮播图...')
      const bannersRes = await get('/banners')
      console.log('轮播图结果:', bannersRes)

      console.log('请求分类...')
      const categoriesRes = await get('/categories')
      console.log('分类结果:', categoriesRes)

      console.log('请求热门菜品...')
      const hotDishesRes = await get('/dish/hot')
      console.log('热门菜品结果:', hotDishesRes)

      console.log('请求新品...')
      const newDishesRes = await get('/dish/new')
      console.log('新品结果:', newDishesRes)

      this.setData({
        banners: bannersRes.data || [],
        categories: categoriesRes.data || [],
        hotDishes: hotDishesRes.data || [],
        newDishes: newDishesRes.data || [],
        loading: false
      })

      console.log('数据加载完成')
    } catch (error) {
      console.error('加载数据失败:', error)
      wx.showModal({
        title: '网络错误',
        content: `连接失败: ${error.message || error.errMsg || '未知错误'}\n\n请检查:\n1. 后端服务是否运行\n2. 网络设置是否正确\n3. 是否开启了"不校验合法域名"`,
        showCancel: false
      })
      this.setData({
        loading: false
      })
    } finally {
      hideLoading()
    }
  },

  // 测试请求方法
  testRequest(url) {
    return new Promise((resolve, reject) => {
      const app = getApp()
      console.log('发起测试请求:', app.globalData.baseUrl + url)

      wx.request({
        url: app.globalData.baseUrl + url,
        method: 'GET',
        header: {
          'Content-Type': 'application/json'
        },
        success: (res) => {
          console.log('请求成功:', res)
          if (res.statusCode === 200) {
            resolve(res.data)
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.data?.message || '请求失败'}`))
          }
        },
        fail: (err) => {
          console.error('请求失败:', err)
          reject(new Error(err.errMsg || '网络请求失败'))
        }
      })
    })
  },

  // 更新购物车数量
  updateCartCount() {
    const app = getApp()
    const cartData = app.getCartData()
    const cartCount = cartData.cart.reduce((total, item) => total + item.quantity, 0)
    const totalPrice = cartData.cart.reduce((total, item) => total + (item.price * item.quantity), 0)

    this.setData({
      cartCount,
      totalPrice
    })

    if (cartCount > 0) {
      wx.setTabBarBadge({
        index: 2,
        text: cartCount.toString()
      })
    } else {
      wx.removeTabBarBadge({
        index: 2
      })
    }
  },

  // 智能搜索
  onSearch(e) {
    const { keyword } = e.detail
    wx.navigateTo({
      url: `/pages/search/search?keyword=${encodeURIComponent(keyword)}`
    })
  },

  // 点击轮播图
  onBannerTap(e) {
    const { item } = e.currentTarget.dataset
    if (item.link_type === 'dish' && item.link_id) {
      wx.navigateTo({
        url: `/pages/detail/detail?id=${item.link_id}`
      })
    } else if (item.link_type === 'category' && item.link_id) {
      wx.navigateTo({
        url: `/pages/category/category?categoryId=${item.link_id}`
      })
    }
  },

  // 点击分类
  onCategoryTap(e) {
    const { item } = e.currentTarget.dataset
    wx.navigateTo({
      url: `/pages/category/category?categoryId=${item.id}`
    })
  },

  // 点击菜品
  onDishTap(e) {
    const { dish } = e.detail
    wx.navigateTo({
      url: `/pages/detail/detail?id=${dish.id}`
    })
  },

  // 添加到购物车
  onAddToCart(e) {
    const { dish } = e.detail
    const app = getApp()

    app.addToCart({
      id: dish.id,
      name: dish.name,
      price: dish.price,
      image: dish.image,
      quantity: 1
    })

    wx.showToast({
      title: '已添加到购物车',
      icon: 'success'
    })

    this.updateCartCount()
  },

  // 切换点赞
  onToggleLike(e) {
    const { dish, isLiked } = e.detail
    // 这里可以调用API保存用户的点赞状态
    console.log(`${isLiked ? '点赞' : '取消点赞'} 菜品:`, dish.name)
  },

  // 悬浮购物车点击
  onFloatingCartTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 悬浮购物车结算
  onFloatingCheckoutTap() {
    wx.switchTab({
      url: '/pages/cart/cart'
    })
  },

  // 查看更多热门菜品
  onMoreHotDishes() {
    wx.navigateTo({
      url: '/pages/category/category?type=hot'
    })
  },

  // 查看更多新品
  onMoreNewDishes() {
    wx.navigateTo({
      url: '/pages/category/category?type=new'
    })
  },

  // 获取图片URL
  getImageUrl(path) {
    return getImageUrl(path)
  },

  // 测试网络连接
  async testConnection() {
    try {
      console.log('开始测试网络连接...')
      const app = getApp()
      const baseUrl = app.globalData.baseUrl

      wx.showModal({
        title: '网络连接测试',
        content: `正在测试连接到: ${baseUrl}`,
        showCancel: false
      })

      // 测试基础连接
      const result = await this.testRequest('/health')

      wx.showModal({
        title: '连接测试成功',
        content: `✅ 后端服务连接正常\n响应: ${result.message}\n\n现在将重新加载数据...`,
        showCancel: false,
        success: () => {
          this.loadData()
        }
      })
    } catch (error) {
      console.error('连接测试失败:', error)
      wx.showModal({
        title: '连接测试失败',
        content: `❌ 无法连接到后端服务\n\n错误信息: ${error.message || error.errMsg}\n\n请检查:\n1. 后端服务是否运行 (localhost:8080)\n2. 微信开发者工具是否开启"不校验合法域名"\n3. 网络设置是否正确`,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.testConnection()
          }
        }
      })
    }
  }
})
