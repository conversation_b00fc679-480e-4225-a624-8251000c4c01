<!--pages/login/login.wxml-->
<view class="container">
  <!-- 头部 -->
  <view class="header">
    <image class="logo" src="/images/logo.png" mode="aspectFit"></image>
    <text class="title">欢迎使用在线点餐</text>
    <text class="subtitle">美食就在指尖</text>
  </view>

  <!-- 登录方式切换 -->
  <view class="login-tabs">
    <view 
      class="tab-item {{loginType === 'phone' ? 'active' : ''}}"
      bindtap="onSwitchLoginType"
      data-type="phone"
    >
      <text>手机号登录</text>
    </view>
    <view 
      class="tab-item {{loginType === 'wechat' ? 'active' : ''}}"
      bindtap="onSwitchLoginType"
      data-type="wechat"
    >
      <text>微信登录</text>
    </view>
  </view>

  <!-- 手机号登录 -->
  <view class="login-form" wx:if="{{loginType === 'phone'}}">
    <!-- 手机号输入 -->
    <view class="input-group">
      <view class="input-label">
        <text class="label-icon">📱</text>
        <text class="label-text">手机号</text>
      </view>
      <input 
        class="input-field"
        type="number"
        placeholder="请输入手机号"
        value="{{phone}}"
        bindinput="onPhoneInput"
        maxlength="11"
      />
    </view>

    <!-- 验证码输入 -->
    <view class="input-group">
      <view class="input-label">
        <text class="label-icon">🔐</text>
        <text class="label-text">验证码</text>
      </view>
      <view class="code-input">
        <input 
          class="input-field"
          type="number"
          placeholder="请输入验证码"
          value="{{code}}"
          bindinput="onCodeInput"
          maxlength="6"
        />
        <button 
          class="code-btn {{canGetCode ? '' : 'disabled'}}"
          bindtap="onGetCode"
          disabled="{{!canGetCode}}"
        >
          {{canGetCode ? '获取验证码' : countdown + 's'}}
        </button>
      </view>
    </view>

    <!-- 登录按钮 -->
    <button 
      class="login-btn"
      bindtap="onPhoneLogin"
      disabled="{{loading}}"
    >
      {{loading ? '登录中...' : '登录'}}
    </button>
  </view>

  <!-- 微信登录 -->
  <view class="wechat-login" wx:if="{{loginType === 'wechat'}}">
    <view class="wechat-info">
      <text class="wechat-icon">💬</text>
      <text class="wechat-title">微信快速登录</text>
      <text class="wechat-desc">使用微信账号快速登录，享受便捷服务</text>
    </view>
    
    <button 
      class="wechat-btn"
      bindtap="onWechatLogin"
      disabled="{{loading}}"
    >
      <text class="wechat-btn-icon">💬</text>
      <text class="wechat-btn-text">{{loading ? '登录中...' : '微信授权登录'}}</text>
    </button>
  </view>

  <!-- 用户协议 -->
  <view class="agreement">
    <text class="agreement-text">
      登录即表示同意
      <text class="agreement-link">《用户协议》</text>
      和
      <text class="agreement-link">《隐私政策》</text>
    </text>
  </view>
</view>
